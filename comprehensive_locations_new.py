import json
import time
import requests
import geonamescache
import osmnx as ox
import overpy
from typing import Dict, List, Set
import logging
from decimal import Decimal
from shapely.geometry import Point, LineString, Polygon, MultiPolygon
import pandas as pd
import asyncio
import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedLocationCollector:
    def __init__(self):
        self.locations = set()
        self.detailed_locations = []
        self.gc = geonamescache.GeonamesCache()

        # Expanded Accra metropolitan area bounding box
        self.accra_bbox = {
            'north': 6.0,   # Expanded north
            'south': 5.1,   # Expanded south
            'east': 0.5,    # Expanded east
            'west': -0.8    # Expanded west
        }

        # Additional smaller bounding boxes for detailed searches
        self.detailed_areas = [
            {'name': 'Central Accra', 'north': 5.65,
                'south': 5.45, 'east': -0.1, 'west': -0.3},
            {'name': 'East Legon', 'north': 5.70,
                'south': 5.60, 'east': -0.1, 'west': -0.2},
            {'name': '<PERSON><PERSON>', 'north': 5.70, 'south': 5.60,
                'east': 0.1, 'west': -0.05},
            {'name': 'Kasoa', 'north': 5.55,
                'south': 5.45, 'east': -0.4, 'west': -0.5},
        ]

        # Configure OSMnx settings
        ox.settings.use_cache = True
        ox.settings.log_console = True

    def get_comprehensive_osm_data(self):
        """Get more comprehensive OSM data with additional categories"""
        logger.info("Fetching comprehensive OSM data...")

        osm_locations = []

        # Expanded list of OSM categories
        osm_categories = {
            'place': ['city', 'town', 'village', 'suburb', 'neighbourhood', 'hamlet',
                      'locality', 'quarter', 'city_block'],
            'amenity': ['school', 'hospital', 'clinic', 'university', 'college', 'bank',
                        'atm', 'restaurant', 'cafe', 'bar', 'pub', 'pharmacy', 'fuel',
                        'parking', 'marketplace', 'police', 'fire_station', 'post_office',
                        'library', 'community_centre', 'social_facility', 'courthouse',
                        'prison', 'embassy', 'townhall', 'kindergarten', 'cinema',
                        'theatre', 'nightclub', 'casino', 'place_of_worship'],
            'shop': ['supermarket', 'convenience', 'clothes', 'electronics', 'bakery',
                     'butcher', 'car', 'car_repair', 'furniture', 'hairdresser',
                     'jewelry', 'mobile_phone', 'shoes', 'sports', 'books',
                     'chemist', 'department_store', 'mall', 'market'],
            'tourism': ['hotel', 'motel', 'guest_house', 'hostel', 'attraction',
                        'viewpoint', 'museum', 'gallery', 'zoo', 'theme_park'],
            'landuse': ['residential', 'commercial', 'industrial', 'retail',
                        'institutional', 'education', 'cemetery', 'recreation_ground'],
            'leisure': ['park', 'playground', 'sports_centre', 'stadium', 'swimming_pool',
                        'golf_course', 'garden', 'beach_resort'],
            'healthcare': ['hospital', 'clinic', 'dentist', 'veterinary', 'pharmacy'],
            'office': ['government', 'company', 'lawyer', 'estate_agent', 'insurance'],
            'building': ['apartments', 'house', 'commercial', 'industrial', 'office',
                         'hotel', 'school', 'hospital', 'church', 'mosque'],
            'highway': ['bus_stop', 'traffic_signals'],
            'public_transport': ['station', 'stop_position', 'platform'],
            'natural': ['beach', 'forest', 'water', 'wetland', 'hill', 'peak'],
            'waterway': ['river', 'stream', 'canal', 'drain'],
            'power': ['station', 'substation', 'generator', 'tower', 'pole'],
            'man_made': ['tower', 'bridge', 'pier', 'water_tower', 'lighthouse'],
            'sport': ['football', 'basketball', 'tennis', 'swimming', 'athletics']
        }

        try:
            place_name = "Accra, Ghana"

            for category, values in osm_categories.items():
                for value in values:
                    try:
                        logger.info(f"Fetching {category}={value} data...")

                        # Create tag query
                        tags = {category: value}

                        gdf = ox.features_from_place(place_name, tags=tags)

                        for idx, row in gdf.iterrows():
                            name = row.get('name')
                            if name and isinstance(name, str) and len(name.strip()) > 0:
                                try:
                                    # Get coordinates
                                    if hasattr(row.geometry, 'centroid'):
                                        centroid = row.geometry.centroid
                                        lat, lon = float(
                                            centroid.y), float(centroid.x)
                                    else:
                                        lat, lon = float(row.geometry.y), float(
                                            row.geometry.x)

                                    # Clean tags
                                    clean_tags = self.clean_tags(
                                        dict(row.dropna()))

                                    location = {
                                        'name': name.strip(),
                                        'type': f"OSM_{category}_{value}",
                                        'source': 'OpenStreetMap_Enhanced',
                                        'coordinates': {'lat': lat, 'lng': lon},
                                        'osm_id': str(idx),
                                        'category': category,
                                        'subcategory': value,
                                        'tags': clean_tags
                                    }

                                    if name.strip() not in self.locations:
                                        osm_locations.append(location)
                                        self.locations.add(name.strip())

                                except Exception as e:
                                    logger.warning(
                                        f"Error processing {category}={value} location {name}: {e}")
                                    continue

                        # Rate limiting
                        time.sleep(0.5)

                    except Exception as e:
                        logger.warning(
                            f"Error fetching {category}={value}: {e}")
                        continue

        except Exception as e:
            logger.error(f"Error with enhanced OSM data: {e}")

        logger.info(f"Found {len(osm_locations)} enhanced OSM locations")
        return osm_locations

    def get_detailed_area_data(self):
        """Get detailed data for specific areas using smaller bounding boxes"""
        logger.info("Fetching detailed area data...")

        detailed_locations = []

        for area in self.detailed_areas:
            logger.info(f"Processing detailed area: {area['name']}")

            bbox = f"{area['south']},{area['west']},{area['north']},{area['east']}"

            # More specific queries for smaller areas
            detailed_queries = [
                f'[out:json][timeout:30];(node["name"]({bbox}););out;',
                f'[out:json][timeout:30];(way["name"]({bbox}););out center;',
                f'[out:json][timeout:30];(relation["name"]({bbox}););out center;',
                f'[out:json][timeout:30];(node["addr:street"]({bbox}););out;',
                f'[out:json][timeout:30];(way["building"]({bbox}););out center;',
            ]

            api = overpy.Overpass()

            for query in detailed_queries:
                try:
                    result = api.query(query)

                    # Process all types of results
                    for element in result.nodes + result.ways + result.relations:
                        name = element.tags.get(
                            'name') or element.tags.get('addr:street')
                        if name and name.strip() not in self.locations:
                            try:
                                if hasattr(element, 'lat') and hasattr(element, 'lon'):
                                    lat, lon = float(
                                        element.lat), float(element.lon)
                                elif hasattr(element, 'center_lat') and hasattr(element, 'center_lon'):
                                    lat, lon = float(element.center_lat), float(
                                        element.center_lon)
                                else:
                                    continue

                                location = {
                                    'name': name.strip(),
                                    'type': f"Detailed_{area['name'].replace(' ', '_')}",
                                    'source': 'DetailedArea',
                                    'coordinates': {'lat': lat, 'lng': lon},
                                    'area': area['name'],
                                    'osm_id': str(element.id),
                                    'tags': self.clean_tags(dict(element.tags))
                                }

                                detailed_locations.append(location)
                                self.locations.add(name.strip())

                            except Exception as e:
                                logger.warning(
                                    f"Error processing detailed location {name}: {e}")
                                continue

                    time.sleep(1)

                except Exception as e:
                    logger.warning(
                        f"Error with detailed query for {area['name']}: {e}")
                    continue

        logger.info(f"Found {len(detailed_locations)} detailed area locations")
        return detailed_locations

    def get_ghana_postal_service_data(self):
        """Attempt to get data from Ghana Post GPS addressing system"""
        logger.info("Attempting to get Ghana Post GPS data...")

        postal_locations = []

        # Ghana Post uses a digital addressing system
        # This is a placeholder for potential API integration
        try:
            # Note: This would require actual API access to Ghana Post
            # For now, we'll use known postal codes and areas
            known_areas = [
                "GA-001", "GA-002", "GA-003", "GA-004", "GA-005",
                "GT-001", "GT-002", "GT-003", "GT-004", "GT-005",
                "AK-001", "AK-002", "AK-003", "AK-004", "AK-005"
            ]

            # This is a mock implementation - replace with actual API calls
            logger.info("Ghana Post GPS data would require API access")

        except Exception as e:
            logger.warning(f"Could not access Ghana Post data: {e}")

        return postal_locations

    def get_street_level_data(self):
        """Get street-level data including addresses and building numbers"""
        logger.info("Fetching street-level data...")

        street_locations = []
        api = overpy.Overpass()

        # Query for streets and addresses
        bbox = f"{self.accra_bbox['south']},{self.accra_bbox['west']},{self.accra_bbox['north']},{self.accra_bbox['east']}"

        street_queries = [
            f'[out:json][timeout:45];(way["highway"]["name"]({bbox}););out center;',
            f'[out:json][timeout:45];(node["addr:housenumber"]["addr:street"]({bbox}););out;',
            f'[out:json][timeout:45];(way["building"]["addr:street"]({bbox}););out center;',
            f'[out:json][timeout:45];(node["entrance"]({bbox}););out;',
        ]

        for query in street_queries:
            try:
                logger.info("Executing street-level query...")
                result = api.query(query)

                # Process streets
                for way in result.ways:
                    name = way.tags.get('name')
                    if name and name.strip() not in self.locations:
                        try:
                            if hasattr(way, 'center_lat') and hasattr(way, 'center_lon'):
                                lat, lon = float(way.center_lat), float(
                                    way.center_lon)
                            else:
                                continue

                            location = {
                                'name': name.strip(),
                                'type': 'Street',
                                'source': 'StreetLevel',
                                'coordinates': {'lat': lat, 'lng': lon},
                                'osm_id': str(way.id),
                                'tags': self.clean_tags(dict(way.tags))
                            }

                            street_locations.append(location)
                            self.locations.add(name.strip())

                        except Exception as e:
                            logger.warning(
                                f"Error processing street {name}: {e}")
                            continue

                # Process address nodes
                for node in result.nodes:
                    addr_street = node.tags.get('addr:street')
                    addr_house = node.tags.get('addr:housenumber')

                    if addr_street:
                        name = f"{addr_house} {addr_street}" if addr_house else addr_street
                        if name.strip() not in self.locations:
                            try:
                                location = {
                                    'name': name.strip(),
                                    'type': 'Address',
                                    'source': 'StreetLevel',
                                    'coordinates': {'lat': float(node.lat), 'lng': float(node.lon)},
                                    'osm_id': str(node.id),
                                    'tags': self.clean_tags(dict(node.tags))
                                }

                                street_locations.append(location)
                                self.locations.add(name.strip())

                            except Exception as e:
                                logger.warning(
                                    f"Error processing address {name}: {e}")
                                continue

                time.sleep(2)

            except Exception as e:
                logger.warning(f"Error with street-level query: {e}")
                continue

        logger.info(f"Found {len(street_locations)} street-level locations")
        return street_locations

    def get_business_directory_data(self):
        """Get business and commercial data"""
        logger.info("Fetching business directory data...")

        business_locations = []
        api = overpy.Overpass()

        bbox = f"{self.accra_bbox['south']},{self.accra_bbox['west']},{self.accra_bbox['north']},{self.accra_bbox['east']}"

        # Business-focused queries
        business_queries = [
            f'[out:json][timeout:30];(node["brand"]({bbox}););out;',
            f'[out:json][timeout:30];(way["brand"]({bbox}););out center;',
            f'[out:json][timeout:30];(node["operator"]({bbox}););out;',
            f'[out:json][timeout:30];(way["operator"]({bbox}););out center;',
            f'[out:json][timeout:30];(node["contact:phone"]({bbox}););out;',
            f'[out:json][timeout:30];(way["contact:phone"]({bbox}););out center;',
        ]

        for query in business_queries:
            try:
                result = api.query(query)

                for element in result.nodes + result.ways:
                    # Try multiple name sources
                    name = (element.tags.get('name') or
                            element.tags.get('brand') or
                            element.tags.get('operator'))

                    if name and name.strip() not in self.locations:
                        try:
                            if hasattr(element, 'lat') and hasattr(element, 'lon'):
                                lat, lon = float(
                                    element.lat), float(element.lon)
                            elif hasattr(element, 'center_lat') and hasattr(element, 'center_lon'):
                                lat, lon = float(element.center_lat), float(
                                    element.center_lon)
                            else:
                                continue

                            location = {
                                'name': name.strip(),
                                'type': 'Business',
                                'source': 'BusinessDirectory',
                                'coordinates': {'lat': lat, 'lng': lon},
                                'osm_id': str(element.id),
                                'tags': self.clean_tags(dict(element.tags))
                            }

                            business_locations.append(location)
                            self.locations.add(name.strip())

                        except Exception as e:
                            logger.warning(
                                f"Error processing business {name}: {e}")
                            continue

                time.sleep(1.5)

            except Exception as e:
                logger.warning(f"Error with business query: {e}")
                continue

        logger.info(f"Found {len(business_locations)} business locations")
        return business_locations

    def clean_tags(self, tags_dict):
        """Clean tags dictionary to remove non-serializable objects"""
        cleaned_tags = {}
        for key, value in tags_dict.items():
            if isinstance(value, (str, int, float, bool, type(None))):
                cleaned_tags[key] = value
            elif isinstance(value, (list, tuple)):
                try:
                    json.dumps(value)
                    cleaned_tags[key] = value
                except (TypeError, ValueError):
                    cleaned_tags[key] = str(value)
            else:
                cleaned_tags[key] = str(value)
        return cleaned_tags

    def collect_all_enhanced_locations(self):
        """Collect locations from all enhanced sources"""
        logger.info("Starting enhanced comprehensive location collection...")

        all_locations = []

        # Original sources (keep your existing methods)
        try:
            geonames_data = self.get_geonames_data()  # Your existing method
            all_locations.extend(geonames_data)
        except Exception as e:
            logger.error(f"Error collecting GeoNames data: {e}")

        # Enhanced OSM data
        try:
            enhanced_osm_data = self.get_comprehensive_osm_data()
            all_locations.extend(enhanced_osm_data)
        except Exception as e:
            logger.error(f"Error collecting enhanced OSM data: {e}")

        # Detailed area data
        try:
            detailed_data = self.get_detailed_area_data()
            all_locations.extend(detailed_data)
        except Exception as e:
            logger.error(f"Error collecting detailed area data: {e}")

        # Street-level data
        try:
            street_data = self.get_street_level_data()
            all_locations.extend(street_data)
        except Exception as e:
            logger.error(f"Error collecting street-level data: {e}")

        # Business directory data
        try:
            business_data = self.get_business_directory_data()
            all_locations.extend(business_data)
        except Exception as e:
            logger.error(f"Error collecting business data: {e}")

        # Your existing Overpass data
        try:
            overpy_data = self.get_overpy_data()  # Your existing method
            all_locations.extend(overpy_data)
        except Exception as e:
            logger.error(f"Error collecting Overpass data: {e}")

        return {
            'country': 'Ghana',
            'region': 'Greater Accra Region',
            'capital': 'Accra',
            'description': 'Enhanced comprehensive list of locations in Accra, Ghana',
            'last_updated': time.strftime('%Y-%m-%d'),
            'total_locations': len(all_locations),
            'data_sources': ['GeoNames', 'OpenStreetMap_Enhanced', 'DetailedArea',
                             'StreetLevel', 'BusinessDirectory', 'Overpass'],
            'bounding_box': self.accra_bbox,
            'locations': all_locations
        }


# Additional strategies you can implement:

def get_wikipedia_locations():
    """Extract location mentions from Wikipedia articles about Accra"""
    # Use Wikipedia API to get articles about Accra and extract location names
    pass


def get_social_media_locations():
    """Extract geotagged locations from social media APIs (if available)"""
    # Twitter, Instagram, Facebook check-ins (requires API access)
    pass


def get_government_data():
    """Get data from Ghana government databases"""
    # Ghana Statistical Service, Ministry of Local Government data
    pass


def get_transport_data():
    """Get transport-related locations"""
    # Bus stops, tro-tro stations, taxi ranks, airports, ports
    pass
# filepath: /Users/<USER>/Kayak/locations/comprehensive_locations_new.py


if __name__ == "__main__":
    collector = EnhancedLocationCollector()
    result = collector.collect_all_enhanced_locations()
    print(json.dumps(result, indent=2))
