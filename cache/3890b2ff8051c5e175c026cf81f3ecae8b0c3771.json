{"version": 0.6, "generator": "Overpass API 0.7.62.7 375dc00a", "osm3s": {"timestamp_osm_base": "2025-06-30T13:07:15Z", "copyright": "The data included in this document is from www.openstreetmap.org. The data is made available under ODbL."}, "elements": [{"type": "node", "id": 1376298428, "lat": 5.559448, "lon": -0.1793356, "tags": {"amenity": "casino", "name": "Diamond Casino"}}, {"type": "node", "id": 1692434889, "lat": 5.5617139, "lon": -0.1822566}, {"type": "node", "id": 1692434891, "lat": 5.5618101, "lon": -0.1819958}, {"type": "node", "id": 1692434899, "lat": 5.5618267, "lon": -0.1819929}, {"type": "node", "id": 1692434901, "lat": 5.5618511, "lon": -0.1822326}, {"type": "node", "id": 2644149763, "lat": 5.5604151, "lon": -0.1824952, "tags": {"amenity": "casino", "name": "Golden Dragon Casino"}}, {"type": "node", "id": 3360413369, "lat": 5.5657012, "lon": -0.1803058, "tags": {"amenity": "casino", "name": "Caesars Casino", "name:it": "Caesars"}}, {"type": "node", "id": 4411572790, "lat": 5.5435128, "lon": -0.2163296, "tags": {"amenity": "casino"}}, {"type": "node", "id": 5541229625, "lat": 5.5936011, "lon": -0.1809223, "tags": {"addr:postcode": "16033", "addr:street": "Liberation Road", "amenity": "casino", "name": "Millionaire <PERSON><PERSON><PERSON><PERSON>", "name:it": "Millionaire"}}, {"type": "node", "id": 5629497744, "lat": 5.5618123, "lon": -0.1819098}, {"type": "node", "id": 5629497745, "lat": 5.5616585, "lon": -0.1819367}, {"type": "node", "id": 7507227995, "lat": 5.5641288, "lon": -0.1812653, "tags": {"amenity": "casino", "name": "Queens Casino"}}, {"type": "node", "id": 9403540530, "lat": 5.6309201, "lon": -0.1434106, "tags": {"amenity": "casino", "name": "86 Casino"}}, {"type": "node", "id": 10616920837, "lat": 5.6136022, "lon": -0.0728454, "tags": {"amenity": "casino", "name": "Vulkan VIP"}}, {"type": "way", "id": 157012447, "nodes": [1692434889, 1692434901, 1692434891, 1692434899, 5629497744, 5629497745, 1692434889], "tags": {"amenity": "casino", "building": "yes", "name": "Piccadilly Casino", "name:it": "Piccadilly", "source": "<PERSON>"}}]}