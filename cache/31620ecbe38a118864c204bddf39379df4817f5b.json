{"version": 0.6, "generator": "Overpass API 0.7.62.7 375dc00a", "osm3s": {"timestamp_osm_base": "2025-06-30T13:06:08Z", "copyright": "The data included in this document is from www.openstreetmap.org. The data is made available under ODbL."}, "elements": [{"type": "node", "id": 567464504, "lat": 5.6423906, "lon": -0.1518119, "tags": {"amenity": "atm"}}, {"type": "node", "id": 1207720467, "lat": 5.5916743, "lon": -0.1810114, "tags": {"amenity": "atm", "cash_in": "yes", "check_date": "2023-09-15", "covered": "yes", "currency:GHS": "yes", "indoor": "no", "name": "SG-SSB", "opening_hours": "24/7", "operator": "SG-SSB"}}, {"type": "node", "id": 1207720484, "lat": 5.6158087, "lon": -0.1754817, "tags": {"amenity": "atm", "currency:ghs": "yes", "name": "SG-SSB (Societe Generale)", "operator": "SG-SSB"}}, {"type": "node", "id": **********, "lat": 5.6006404, "lon": -0.1974603, "tags": {"amenity": "atm", "operator": "SG-SSB"}}, {"type": "node", "id": **********, "lat": 5.6478457, "lon": -0.1789641, "tags": {"amenity": "atm", "name": "SG-SSB", "operator": "SG-SSB"}}, {"type": "node", "id": **********, "lat": 5.6002874, "lon": -0.1783876, "tags": {"amenity": "atm", "brand": "Stanbic Bank", "brand:wikidata": "Q7597999", "cash_in": "yes", "check_date": "2024-11-22", "name": "Stanbic Bank", "operator": "Stanbic Bank", "operator:wikidata": "Q7597999"}}, {"type": "node", "id": **********, "lat": 5.6000298, "lon": -0.1783058, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "cash_in": "yes", "check_date": "2024-11-22", "name": "Ecobank", "opening_hours": "24/7", "operator": "Ecobank", "operator:wikidata": "Q930225"}}, {"type": "node", "id": **********, "lat": 5.5638488, "lon": -0.1404223, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "SG-SSB"}}, {"type": "node", "id": **********, "lat": 5.6084433, "lon": -0.2095812, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "brand:wikipedia": "en:GCB Bank", "name": "GCB Bank", "operator": "GCB Bank", "operator:wikidata": "Q1521346", "operator:wikipedia": "en:GCB Bank"}}, {"type": "node", "id": **********, "lat": 5.5602172, "lon": -0.2021078, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "name": "Ecobank", "operator": "Ecobank", "operator:wikidata": "Q930225"}}, {"type": "node", "id": **********, "lat": 5.5933823, "lon": -0.1809338, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "GCB"}}, {"type": "node", "id": **********, "lat": 5.5933967, "lon": -0.1809973, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "<PERSON><PERSON>"}}, {"type": "node", "id": **********, "lat": 5.6078559, "lon": -0.2073811, "tags": {"amenity": "atm", "name": "<PERSON><PERSON>", "operator": "<PERSON><PERSON>"}}, {"type": "node", "id": **********, "lat": 5.6138343, "lon": -0.1864895, "tags": {"amenity": "atm", "brand": "Access Bank", "brand:wikidata": "Q4672418", "name": "Access Bank", "operator": "Access Bank", "operator:wikidata": "Q4672418"}}, {"type": "node", "id": **********, "lat": 5.5657164, "lon": -0.1810146, "tags": {"amenity": "atm", "brand": "Fidelity Bank", "brand:wikidata": "Q5446778", "check_date": "2025-03-10", "name": "Fidelity Bank", "operator": "Fidelity Bank", "operator:wikidata": "Q5446778"}}, {"type": "node", "id": **********, "lat": 5.5657313, "lon": -0.1810587, "tags": {"amenity": "atm", "brand": "SG-SSB", "check_date": "2025-03-10", "operator": "SG-SSB"}}, {"type": "node", "id": **********, "lat": 5.5682472, "lon": -0.2198294, "tags": {"amenity": "atm", "operator": "CAL Bank"}}, {"type": "node", "id": **********, "lat": 5.5689107, "lon": -0.2221403, "tags": {"amenity": "atm", "operator": "SCB"}}, {"type": "node", "id": **********, "lat": 5.5660796, "lon": -0.2358127, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "check_date": "2023-01-13", "operator": "GCB Bank", "operator:wikidata": "Q1521346"}}, {"type": "node", "id": **********, "lat": 5.5684837, "lon": -0.1682024, "tags": {"amenity": "atm", "brand": "Stanbic Bank", "brand:wikidata": "Q7597999", "brand:wikipedia": "en:Stanbic Bank", "name": "Stanbic Bank", "opening_hours": "24/7", "operator": "Stanbic Bank", "operator:wikidata": "Q7597999", "operator:wikipedia": "en:Stanbic Bank"}}, {"type": "node", "id": **********, "lat": 5.5981666, "lon": -0.233036, "tags": {"amenity": "atm", "name": "<PERSON><PERSON>"}}, {"type": "node", "id": **********, "lat": 5.5902667, "lon": -0.18105, "tags": {"amenity": "atm", "brand": "CAL Bank", "check_date": "2024-11-22", "opening_hours": "24/7"}}, {"type": "node", "id": **********, "lat": 5.5661966, "lon": -0.1700406, "tags": {"amenity": "atm", "name": "Republic Bank"}}, {"type": "node", "id": **********, "lat": 5.6176973, "lon": -0.1921497, "tags": {"amenity": "atm", "brand": "Stanbic Bank", "brand:wikidata": "Q7597999", "name": "Stanbic Bank", "operator": "Stanbic Bank", "operator:wikidata": "Q7597999"}}, {"type": "node", "id": **********, "lat": 5.6043256, "lon": -0.1837383, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "name": "Ecobank", "operator": "Ecobank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.5998852, "lon": -0.1896081, "tags": {"amenity": "atm", "brand": "UBA"}}, {"type": "node", "id": **********, "lat": 5.6493371, "lon": -0.1870528, "tags": {"amenity": "atm", "brand": "Republic Bank", "brand:wikidata": "Q7314386", "cash_in": "yes", "name": "Republic Bank", "old_name": "hfc bank", "operator": "Republic Bank", "operator:type": "public", "operator:wikidata": "Q7314386"}}, {"type": "node", "id": **********, "lat": 5.6507859, "lon": -0.1890544, "tags": {"amenity": "atm", "brand": "CalBank", "brand:wikidata": "Q4035559", "opening_hours": "24/7", "operator": "CalBank", "operator:type": "public", "operator:wikidata": "Q4035559"}}, {"type": "node", "id": **********, "lat": 5.6469774, "lon": -0.1865162, "tags": {"amenity": "atm", "cash_in": "yes", "check_date": "2024-11-13", "opening_hours": "24/7", "operator": "All Banks"}}, {"type": "node", "id": **********, "lat": 5.6185056, "lon": -0.1924774, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "name": "Ecobank", "operator": "Ecobank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.6519994, "lon": -0.188485, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "name": "Ecobank", "operator": "Ecobank", "operator:type": "public", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.5357511, "lon": -0.2290899, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "brand:wikipedia": "en:GCB Bank", "name": "GCB Bank", "operator": "GCB Bank", "operator:wikidata": "Q1521346", "operator:wikipedia": "en:GCB Bank"}}, {"type": "node", "id": **********, "lat": 5.5636408, "lon": -0.1724314, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Ndabaningi Sithole", "addr:suburb": "Labone", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "old_name": "Barclays", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": **********, "lat": 5.5678403, "lon": -0.1692712, "tags": {"amenity": "atm", "brand": "CalBank", "brand:wikidata": "Q4035559", "contact:linkedin": "calbank-plc", "contact:twitter": "CalBankPLC", "contact:whatsapp": "+233 24 242 6398", "contact:youtube": "https://www.youtube.com/channel/UCzczU8uI8AFTaXfM-2GDhYQ", "email": "<EMAIL>", "facebook": "CalBankPLC", "name": "CalBank ATM", "opening_hours": "24/7", "operator": "CalBank", "operator:type": "public", "operator:wikidata": "Q4035559", "operator:wikipedia": "en:CalBank", "website": "https://calbank.net"}}, {"type": "node", "id": **********, "lat": 5.5451641, "lon": -0.2066448, "tags": {"amenity": "atm", "name": "Best Point"}}, {"type": "node", "id": **********, "lat": 5.6381547, "lon": -0.1261119, "tags": {"amenity": "atm", "check_date": "2024-11-16", "opening_hours": "24/7", "operator": "Sahel Sahara"}}, {"type": "node", "id": **********, "lat": 5.6304929, "lon": -0.144815, "tags": {"amenity": "atm", "operator": "Consolidated"}}, {"type": "node", "id": **********, "lat": 5.63282, "lon": -0.1392107, "tags": {"amenity": "atm", "name": "FBN Bank", "opening_hours": "24/7", "operator": "Fbn"}}, {"type": "node", "id": **********, "lat": 5.6328593, "lon": -0.1391059, "tags": {"amenity": "atm", "operator": "<PERSON><PERSON>"}}, {"type": "node", "id": **********, "lat": 5.6292201, "lon": -0.1465303, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "GT Bank"}}, {"type": "node", "id": **********, "lat": 5.63427, "lon": -0.1343161, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Spintex Road", "addr:suburb": "Spintex", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": **********, "lat": 5.6324496, "lon": -0.1391051, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "brand:wikipedia": "en:GCB Bank", "name": "GCB Bank", "opening_hours": "24/7", "operator": "GCB Bank", "operator:wikidata": "Q1521346", "operator:wikipedia": "en:GCB Bank"}}, {"type": "node", "id": **********, "lat": 5.5546911, "lon": -0.2168483, "tags": {"amenity": "atm", "name": "GCB"}}, {"type": "node", "id": **********, "lat": 5.5546241, "lon": -0.2163015, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "<PERSON><PERSON>"}}, {"type": "node", "id": **********, "lat": 5.5737554, "lon": -0.19122, "tags": {"amenity": "atm", "check_date": "2024-11-07", "name": "Republic Bank", "old_name": "hfc bank", "opening_hours": "24/7", "operator": "Republic"}}, {"type": "node", "id": **********, "lat": 5.6424837, "lon": -0.1520444, "tags": {"amenity": "atm", "operator": "The Royal Bank"}}, {"type": "node", "id": **********, "lat": 5.642473, "lon": -0.15209, "tags": {"amenity": "atm", "operator": "Stanbic Bank"}}, {"type": "node", "id": **********, "lat": 5.6424577, "lon": -0.1521382, "tags": {"amenity": "atm", "operator": "GCB Bank"}}, {"type": "node", "id": **********, "lat": 5.5736093, "lon": -0.19778, "tags": {"amenity": "atm", "brand": "Société Générale", "brand:wikidata": "Q270363", "check_date": "2024-01-08", "currency:EUR": "yes", "currency:GHS": "yes", "currency:USD": "yes", "drive_through": "no", "name": "Societe Generale Ghana", "name:en": "Societe Generale Ghana", "name:fr": "Société Générale Ghana", "old_name": "SGSSB", "opening_hours": "24/7", "operator": "Société Générale", "operator:wikidata": "Q270363", "website": "https://societegenerale.com.gh/"}}, {"type": "node", "id": **********, "lat": 5.5581841, "lon": -0.2655051, "tags": {"amenity": "atm", "name": "Republic Bank", "old_name": "hfc Bank", "operator": "Republic Bank (Ghana) Limited"}}, {"type": "node", "id": **********, "lat": 5.5581833, "lon": -0.2654807, "tags": {"amenity": "atm", "name": "GCBBank"}}, {"type": "node", "id": **********, "lat": 5.5581851, "lon": -0.2654507, "tags": {"amenity": "atm", "name": "GTBank"}}, {"type": "node", "id": **********, "lat": 5.5581872, "lon": -0.2655389, "tags": {"amenity": "atm", "name": "umb"}}, {"type": "node", "id": **********, "lat": 5.5902626, "lon": -0.1809489, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Liberation Road", "addr:suburb": "Cantoments", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "check_date": "2024-11-27", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "old_name": "Barclays Bank", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": **********, "lat": 5.5902134, "lon": -0.1809902, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "check_date": "2024-11-22", "name": "Ecobank", "opening_hours": "24/7", "operator": "Ecobank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.5646586, "lon": -0.2257313, "tags": {"amenity": "atm", "name": "Republic Bank", "old_name": "hfc bank", "operator": "Republic Bank (Ghana) Limited"}}, {"type": "node", "id": **********, "lat": 5.6595586, "lon": -0.168203, "tags": {"amenity": "atm"}}, {"type": "node", "id": **********, "lat": 5.6595372, "lon": -0.1676129, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "name": "Ecobank", "operator": "Ecobank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.5734982, "lon": -0.1911386, "tags": {"amenity": "atm", "cash_in": "yes", "check_date": "2023-06-05", "currency:GHS": "yes", "opening_hours": "24/7", "operator": "Republic", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.5903839, "lon": -0.1814732, "tags": {"amenity": "atm", "check_date": "2023-06-23", "opening_hours": "24/7", "operator": "Energy"}}, {"type": "node", "id": **********, "lat": 5.5794815, "lon": -0.1557095, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "brand:wikipedia": "en:GCB Bank", "name": "GCB Bank", "old_name": "Ghana Commercial Bank", "operator": "GCB Bank", "operator:type": "public", "operator:wikidata": "Q1521346", "operator:wikipedia": "en:GCB Bank"}}, {"type": "node", "id": **********, "lat": 5.5795357, "lon": -0.1556823, "tags": {"amenity": "atm", "brand": "UBA", "brand:wikidata": "Q513457", "brand:wikipedia": "en:United Bank for Africa", "name": "UBA", "official_name": "United Bank for Africa", "operator": "UBA", "operator:wikidata": "Q513457", "operator:wikipedia": "en:United Bank for Africa"}}, {"type": "node", "id": **********, "lat": 5.5796388, "lon": -0.1556299, "tags": {"amenity": "atm", "brand": "Access Bank", "brand:wikidata": "Q4672418", "brand:wikipedia": "en:Access Bank plc", "name": "Access Bank", "operator": "Access Bank", "operator:wikidata": "Q4672418", "operator:wikipedia": "en:Access Bank plc"}}, {"type": "node", "id": **********, "lat": 5.5792282, "lon": -0.154983, "tags": {"amenity": "atm"}}, {"type": "node", "id": **********, "lat": 5.5795848, "lon": -0.1556571, "tags": {"amenity": "atm", "name": "NIB"}}, {"type": "node", "id": **********, "lat": 5.563196, "lon": -0.1913609, "tags": {"amenity": "atm", "brand": "ADB", "operator": "ADB"}}, {"type": "node", "id": **********, "lat": 5.5620861, "lon": -0.181603, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "brand:wikipedia": "en:GCB Bank", "name": "GCB Bank", "operator": "GCB Bank", "operator:wikidata": "Q1521346", "operator:wikipedia": "en:GCB Bank"}}, {"type": "node", "id": **********, "lat": 5.5620909, "lon": -0.1816222, "tags": {"amenity": "atm", "brand": "Stanbic Bank", "brand:wikidata": "Q7597999", "brand:wikipedia": "en:Stanbic Bank", "name": "Stanbic Bank", "operator": "Stanbic Bank", "operator:wikidata": "Q7597999", "operator:wikipedia": "en:Stanbic Bank"}}, {"type": "node", "id": **********, "lat": 5.5620968, "lon": -0.181646, "tags": {"amenity": "atm"}}, {"type": "node", "id": **********, "lat": 5.562087, "lon": -0.1816749, "tags": {"amenity": "atm", "name": "Prudential"}}, {"type": "node", "id": **********, "lat": 5.5621102, "lon": -0.1816962, "tags": {"amenity": "atm", "name": "OmniBank"}}, {"type": "node", "id": **********, "lat": 5.6288707, "lon": -0.1481053, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "currency:others": "no", "name": "Ecobank", "opening_hours": "24/7", "operator": "Eco Bank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.6375726, "lon": -0.1261746, "tags": {"amenity": "atm", "cash_in": "no", "check_date": "2024-11-16", "opening_hours": "24/7", "operator": "Cal Bank"}}, {"type": "node", "id": **********, "lat": 5.6409722, "lon": -0.1196189, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "UBA"}}, {"type": "node", "id": **********, "lat": 5.6084785, "lon": -0.2094508, "tags": {"amenity": "atm", "name": "Prudential Bank ATM", "opening_hours": "24/7"}}, {"type": "node", "id": **********, "lat": 5.5717914, "lon": -0.2158832, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "cash_in": "yes", "check_date": "2023-05-03", "currency:EUR": "yes", "currency:GHS": "yes", "currency:USD": "yes", "drive_through": "no", "name": "Ecobank", "opening_hours": "Mo-Fr 08:30-16:00; <PERSON><PERSON><PERSON>,PH off", "operator": "Ecobank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.6053527, "lon": -0.083697, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "brand:wikipedia": "en:GCB Bank", "name": "GCB Bank", "opening_hours": "24/7", "operator": "GBC", "operator:wikidata": "Q1521346", "operator:wikipedia": "en:GCB Bank"}}, {"type": "node", "id": **********, "lat": 5.6059674, "lon": -0.1157602, "tags": {"amenity": "atm", "brand": "Stanbic Bank", "brand:wikidata": "Q7597999", "brand:wikipedia": "en:Stanbic Bank", "name": "Stanbic Bank", "opening_hours": "24/7", "operator": "Stanbic Bank", "operator:wikidata": "Q7597999", "operator:wikipedia": "en:Stanbic Bank"}}, {"type": "node", "id": **********, "lat": 5.6222362, "lon": -0.1731901, "tags": {"amenity": "atm"}}, {"type": "node", "id": **********, "lat": 5.6222474, "lon": -0.1731936, "tags": {"amenity": "atm", "brand": "GT Bank", "brand:wikidata": "Q579747", "brand:wikipedia": "en:Guaranty Trust Bank", "name": "GT Bank", "official_name": "Guaranty Trust Bank", "operator": "GT Bank", "operator:wikidata": "Q579747", "operator:wikipedia": "en:Guaranty Trust Bank"}}, {"type": "node", "id": **********, "lat": 5.6222537, "lon": -0.1731993, "tags": {"amenity": "atm", "brand": "UBA", "brand:wikidata": "Q513457", "brand:wikipedia": "en:United Bank for Africa", "name": "UBA", "official_name": "United Bank for Africa", "operator": "UBA", "operator:wikidata": "Q513457", "operator:wikipedia": "en:United Bank for Africa"}}, {"type": "node", "id": **********, "lat": 5.6222561, "lon": -0.1732067, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "<PERSON><PERSON><PERSON> Quarshie Interchange", "addr:suburb": "Airport City", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "old_name": "Barclays", "opening_hours": "24/7", "operator": "Absa", "operator:type": "public", "operator:wikidata": "Q58641733", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": **********, "lat": 5.6222564, "lon": -0.1732157, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "<PERSON><PERSON><PERSON> Quarshie Interchange", "addr:suburb": "Airport City", "alt_name": "Standard Chartered Accra Mall ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered", "operator:type": "public", "operator:wikidata": "Q548278", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": **********, "lat": 5.6034285, "lon": -0.1765168, "tags": {"amenity": "atm", "branch": "Airport City", "brand": "CalBank", "brand:wikidata": "Q4035559", "check_date": "2023-04-28", "name": "CalBank ATM", "opening_hours": "24/7", "operator": "CalBank", "operator:type": "public", "operator:wikidata": "Q4035559", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.6034177, "lon": -0.1765319, "tags": {"amenity": "atm", "branch": "Airport City", "check_date": "2023-04-28", "currency:EUR": "yes", "currency:USD": "yes", "drive_through": "no", "name": "Heritage Bank", "opening_hours": "24/7", "source": "local knowledge"}}, {"type": "node", "id": **********, "lat": 5.6034078, "lon": -0.1765457, "tags": {"amenity": "atm", "branch": "Airport City", "brand": "Société Générale", "brand:wikidata": "Q270363", "check_date": "2023-04-28", "name": "Société Générale", "name:en": "Societe Generale Ghana", "name:fr": "Société Générale Ghana", "old_name": "SGSSB", "opening_hours": "24/7", "operator": "Société Générale", "operator:wikidata": "Q270363", "source": "local knowledge", "website": "https://societegenerale.com.gh/"}}, {"type": "node", "id": **********, "lat": 5.6516214, "lon": -0.1891882, "tags": {"amenity": "atm", "name": "<PERSON><PERSON>"}}, {"type": "node", "id": **********, "lat": 5.6414988, "lon": -0.1090781, "tags": {"amenity": "atm", "brand": "Fidelity Bank", "brand:wikidata": "Q5446778", "brand:wikipedia": "en:Fidelity Bank Ghana", "name": "Fidelity Bank", "operator": "Fidelity Bank", "operator:type": "public", "operator:wikidata": "Q5446778", "operator:wikipedia": "en:Fidelity Bank Ghana"}}, {"type": "node", "id": **********, "lat": 5.6408591, "lon": -0.1189885, "tags": {"amenity": "atm", "check_date": "2025-02-15", "fixme": "First National Bank", "name": "First National Bank"}}, {"type": "node", "id": **********, "lat": 5.6084882, "lon": -0.2094147, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "name": "Ecobank ATM", "opening_hours": "24/7", "operator": "Ecobank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.6084986, "lon": -0.2093858, "tags": {"amenity": "atm", "name": "Stanbic ATM", "opening_hours": "24/7"}}, {"type": "node", "id": **********, "lat": 5.6366501, "lon": -0.103939, "tags": {"amenity": "atm", "brand": "Bank of Africa", "brand:wikidata": "Q2882627", "brand:wikipedia": "en:Bank of Africa Group", "name": "Bank of Africa", "operator": "Bank of Africa", "operator:wikidata": "Q2882627", "operator:wikipedia": "en:Bank of Africa Group", "short_name": "BOA"}}, {"type": "node", "id": **********, "lat": 5.5629673, "lon": -0.2033677, "tags": {"amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "drive_through": "no", "name": "Absa", "old_name": "Barclays", "operator": "Absa Group Limited"}}, {"type": "node", "id": **********, "lat": 5.5506328, "lon": -0.1997875, "tags": {"amenity": "atm", "name": "Fidelity"}}, {"type": "node", "id": **********, "lat": 5.5982926, "lon": -0.2330892, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Abeka Road", "addr:suburb": "<PERSON><PERSON><PERSON>", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa", "operator:type": "public", "operator:wikidata": "Q58641733", "operator:wikipedia": "en:Absa Bank Ghana", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": **********, "lat": 5.591362, "lon": -0.180519, "tags": {"amenity": "atm", "branch": "37", "cash_in": "yes", "check_date": "2023-09-15", "covered": "yes", "currency:GHS": "yes", "indoor": "no", "name": "Access", "opening_hours": "24/7", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.5913764, "lon": -0.1805127, "tags": {"amenity": "atm", "branch": "37", "check_date": "2023-09-15", "description": "Wine and liquor shop", "indoor": "yes", "name": "First Atlantic", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.6508028, "lon": -0.1890253, "tags": {"amenity": "atm", "brand": "Fidelity Bank", "brand:wikidata": "Q5446778", "cash_in": "no", "opening_hours": "24/7", "operator": "Fidelity Bank", "operator:type": "public", "operator:wikidata": "Q5446778"}}, {"type": "node", "id": **********, "lat": 5.6508039, "lon": -0.1889738, "tags": {"amenity": "atm", "brand": "Prudential Bank", "brand:wikidata": "Q7252979", "cash_in": "no", "opening_hours": "24/7", "operator": "Prudential Bank", "operator:type": "private", "operator:wikidata": "Q7252979"}}, {"type": "node", "id": **********, "lat": 5.646781, "lon": -0.1875734, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "brand:wikipedia": "en:GCB Bank", "name": "GCB Bank", "operator": "GCB Bank", "operator:type": "public", "operator:wikidata": "Q1521346", "operator:wikipedia": "en:GCB Bank"}}, {"type": "node", "id": **********, "lat": 5.6468339, "lon": -0.1875034, "tags": {"amenity": "atm", "brand": "Zenith Bank", "brand:wikidata": "Q5978240", "brand:wikipedia": "en:Zenith Bank", "check_date": "2025-02-21", "name": "Zenith Bank", "operator": "Zenith Bank", "operator:wikidata": "Q5978240", "operator:wikipedia": "en:Zenith Bank"}}, {"type": "node", "id": **********, "lat": 5.6471783, "lon": -0.1875033, "tags": {"amenity": "atm", "brand": "Access Bank", "brand:wikidata": "Q4672420", "brand:wikipedia": "en:Access Bank plc", "cash_in": "no", "name": "Access Bank", "operator": "Access Bank", "operator:type": "public", "operator:wikidata": "Q4672420", "operator:wikipedia": "en:Access Bank plc"}}, {"type": "node", "id": **********, "lat": 5.5550374, "lon": -0.2011401, "tags": {"amenity": "atm", "check_date": "2025-06-21", "name": "First National Bank"}}, {"type": "node", "id": **********, "lat": 5.5578034, "lon": -0.2053675, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "name": "Ecobank", "operator": "Ecobank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.5769876, "lon": -0.2279777, "tags": {"amenity": "atm", "name": "<PERSON><PERSON>"}}, {"type": "node", "id": **********, "lat": 5.5588577, "lon": -0.2004381, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Cruickshank Road", "addr:suburb": "Ridge", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": **********, "lat": 5.5713162, "lon": -0.2545749, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Dansoman Road", "addr:suburb": "<PERSON><PERSON><PERSON>", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "old_name": "Barclays Bank", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": **********, "lat": 5.5510052, "lon": -0.1963491, "tags": {"amenity": "atm", "name": "Access ATM"}}, {"type": "node", "id": **********, "lat": 5.6104784, "lon": -0.2353942, "tags": {"addr:city": "Accra", "addr:suburb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "name": "Ecobank", "network": "MasterCard", "operator": "Ecobank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": **********, "lat": 5.588014, "lon": -0.1831925, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "brand:wikipedia": "en:GCB Bank", "check_date": "2023-12-11", "currency:GHS": "yes", "name": "GCB Bank", "opening_hours": "24/7", "operator": "GCB Bank", "operator:wikidata": "Q1521346", "operator:wikipedia": "en:GCB Bank", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.5880999, "lon": -0.1832954, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "RSM Sefordzi Drive", "addr:suburb": "Kanda", "alt_name": "Standard Chartered 37 ATM", "amenity": "atm", "branch": "37 Military Hospital", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "check_date": "2023-12-11", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": **********, "lat": 5.5880835, "lon": -0.1832875, "tags": {"amenity": "atm", "check_date": "2023-12-11", "currency:GHS": "yes", "name": "FBN Bank", "opening_hours": "24/7"}}, {"type": "node", "id": **********, "lat": 5.5880325, "lon": -0.1832388, "tags": {"amenity": "atm", "brand": "UBA", "brand:wikidata": "Q513457", "brand:wikipedia": "en:United Bank for Africa", "check_date": "2023-12-11", "name": "UBA", "official_name": "United Bank for Africa", "opening_hours": "24/7", "operator": "UBA", "operator:wikidata": "Q513457", "operator:wikipedia": "en:United Bank for Africa"}}, {"type": "node", "id": **********, "lat": 5.6265079, "lon": -0.1545004, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "name": "Ecobank", "opening_hours": "24/7", "operator": "Ecobank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.5987065, "lon": -0.1796262, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Liberation Road", "addr:suburb": "Airport City", "alt_name": "Standard Chartered Opeibea ATM", "amenity": "atm", "branch": "Airport City", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "yes", "check_date": "2023-04-28", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "currency:EUR": "yes", "currency:USD": "yes", "description": "Ground Floor, Opeibea House", "drive_through": "no", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "source": "local knowledge", "website": "https://www.sc.com/gh", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.5640165, "lon": -0.1950795, "tags": {"amenity": "atm", "brand": "CalBank", "brand:wikidata": "Q4035559", "contact:linkedin": "calbank-plc", "contact:twitter": "CalBankPLC", "contact:whatsapp": "+233 24 242 6398", "contact:youtube": "https://www.youtube.com/channel/UCzczU8uI8AFTaXfM-2GDhYQ", "email": "<EMAIL>", "facebook": "CalBankPLC", "name": "CalBank ATM", "operator": "CalBank", "operator:type": "public", "operator:wikidata": "Q4035559", "operator:wikipedia": "en:CalBank", "website": "https://calbank.net"}}, {"type": "node", "id": **********, "lat": 5.5546656, "lon": -0.211362, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "brand:wikipedia": "en:GCB Bank", "cash_in": "yes", "check_date": "2024-11-05", "name": "GCB Bank", "opening_hours": "24/7", "operator": "GCB Bank", "operator:wikidata": "Q1521346", "operator:wikipedia": "en:GCB Bank"}}, {"type": "node", "id": **********, "lat": 5.5640885, "lon": -0.1387917, "tags": {"amenity": "atm", "cash_in": "no", "opening_hours": "24/7", "operator": "GCB"}}, {"type": "node", "id": **********, "lat": 5.5770984, "lon": -0.1103071, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "UBA"}}, {"type": "node", "id": **********, "lat": 5.5859838, "lon": -0.1011137, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "ADB"}}, {"type": "node", "id": **********, "lat": 5.6003389, "lon": -0.0799334, "tags": {"amenity": "atm", "check_date": "2024-02-02", "currency:GHS": "yes", "opening_hours": "24/7", "operator": "ADB"}}, {"type": "node", "id": **********, "lat": 5.5895836, "lon": -0.0963644, "tags": {"amenity": "atm", "brand": "Stanbic Bank", "brand:wikidata": "Q7597999", "brand:wikipedia": "en:Stanbic Bank", "currency:others": "no", "drive_through": "no", "name": "Stanbic Bank", "operator": "Stanbic Bank", "operator:wikidata": "Q7597999", "operator:wikipedia": "en:Stanbic Bank"}}, {"type": "node", "id": **********, "lat": 5.6003289, "lon": -0.1973081, "tags": {"amenity": "atm", "name": "Societe General", "opening_hours": "24/7"}}, {"type": "node", "id": **********, "lat": 5.564095, "lon": -0.1422342, "tags": {"amenity": "atm", "brand": "Fidelity Bank", "brand:wikidata": "Q5446778", "brand:wikipedia": "en:Fidelity Bank Ghana", "check_date": "2024-11-17", "drive_through": "no", "name": "Fidelity Bank", "operator": "Fidelity Bank", "operator:type": "public", "operator:wikidata": "Q5446778", "operator:wikipedia": "en:Fidelity Bank Ghana"}}, {"type": "node", "id": **********, "lat": 5.5719027, "lon": -0.1909575, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Independence Avenue", "addr:suburb": "North Ridge", "alt_name": "Standard Chartered Head Office ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "yes", "check_date": "2024-02-05", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "covered": "yes", "currency:GHS": "yes", "description": "Head Office Building, Ground Floor", "drive_through": "no", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "lit": "yes", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "source": "local knowledge", "website": "https://www.sc.com/gh", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.6132308, "lon": -0.0744553, "tags": {"amenity": "atm", "name": "Fidelity", "note": "likely affected due to construction, please reuse, don't just delete"}}, {"type": "node", "id": **********, "lat": 5.5762849, "lon": -0.1525486, "tags": {"amenity": "atm", "brand": "CalBank", "brand:wikidata": "Q4035559", "opening_hours": "24/7", "operator": "CalBank", "operator:type": "public", "operator:wikidata": "Q4035559"}}, {"type": "node", "id": **********, "lat": 5.5726143, "lon": -0.2022744, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "check_date": "2024-11-04", "name": "Ecobank", "operator": "Ecobank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.5976642, "lon": -0.082194, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "Access Bank"}}, {"type": "node", "id": **********, "lat": 5.6287044, "lon": -0.0900894, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "name": "Ecobank", "opening_hours": "24/7", "operator": "Ecobank", "operator:type": "public", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.6388567, "lon": -0.2094688, "tags": {"amenity": "atm", "brand": "Stanbic Bank", "brand:wikidata": "Q7597999", "brand:wikipedia": "en:Stanbic Bank", "name": "Stanbic Bank", "operator": "Stanbic Bank", "operator:wikidata": "Q7597999", "operator:wikipedia": "en:Stanbic Bank"}}, {"type": "node", "id": **********, "lat": 5.6388272, "lon": -0.2094948, "tags": {"amenity": "atm", "name": "UMB"}}, {"type": "node", "id": **********, "lat": 5.5964793, "lon": -0.2229653, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "check_date": "2024-11-06", "name": "Ecobank", "operator": "Ecobank", "operator:wikidata": "Q930225"}}, {"type": "node", "id": **********, "lat": 5.5573157, "lon": -0.2394712, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:suburb": "Larterbiokorshie", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": **********, "lat": 5.5908865, "lon": -0.1807567, "tags": {"amenity": "atm", "cash_in": "yes", "check_date": "2023-06-05", "covered": "no", "currency:GHS": "yes", "indoor": "no", "opening_hours": "24/7", "operator": "CalBank", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.6066217, "lon": -0.2471498, "tags": {"amenity": "atm", "brand": "Prudential Bank", "brand:wikidata": "Q7252979", "ezwich": "yes", "name": "Prudential Bank", "opening_hours": "24/7", "operator": "Prudential Bank", "operator:type": "private", "operator:wikidata": "Q7252979"}}, {"type": "node", "id": **********, "lat": 5.587739, "lon": -0.0993451, "tags": {"amenity": "atm", "name": "CAL Bank"}}, {"type": "node", "id": **********, "lat": 5.5664049, "lon": -0.1896394, "tags": {"amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "cash_in": "yes", "check_date": "2025-03-22", "name": "Absa", "old_name": "Barclays", "operator": "Absa Group Limited"}}, {"type": "node", "id": **********, "lat": 5.5596958, "lon": -0.1977227, "tags": {"amenity": "atm", "brand": "GT Bank", "brand:wikidata": "Q579747", "cash_in": "yes", "check_date": "2024-09-21", "name": "GT Bank", "official_name": "Guaranty Trust Bank", "operator": "GT Bank", "operator:wikidata": "Q579747"}}, {"type": "node", "id": **********, "lat": 5.6259665, "lon": -0.0826842, "tags": {"amenity": "atm", "brand": "Fidelity Bank", "brand:wikidata": "Q5446778", "brand:wikipedia": "en:Fidelity Bank Ghana", "name": "Fidelity Bank", "operator": "Fidelity Bank", "operator:type": "public", "operator:wikidata": "Q5446778", "operator:wikipedia": "en:Fidelity Bank Ghana"}}, {"type": "node", "id": **********, "lat": 5.6378009, "lon": -0.1714665, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "brand:wikipedia": "en:Ecobank", "name": "Ecobank", "operator": "Ecobank", "operator:wikidata": "Q930225", "operator:wikipedia": "en:Ecobank"}}, {"type": "node", "id": **********, "lat": 5.6301937, "lon": -0.1746693, "tags": {"amenity": "atm", "name": "UMB"}}, {"type": "node", "id": **********, "lat": 5.565334, "lon": -0.1816294, "tags": {"amenity": "atm", "check_date": "2025-03-10", "name": "First National Bank"}}, {"type": "node", "id": **********, "lat": 5.5570551, "lon": -0.1661134, "tags": {"amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "check_date": "2025-02-18", "name": "Standard Chartered", "operator": "Standard Chartered", "operator:wikidata": "Q548278", "operator:wikipedia": "en:Standard Chartered"}}, {"type": "node", "id": **********, "lat": 5.5722805, "lon": -0.2033363, "tags": {"amenity": "atm", "brand": "GT Bank", "brand:wikidata": "Q579747", "brand:wikipedia": "en:Guaranty Trust Bank", "cash_in": "yes", "check_date": "2023-04-28", "currency:EUR": "yes", "currency:USD": "yes", "drive_through": "no", "name": "GT Bank", "official_name": "Guaranty Trust Bank", "operator": "GT Bank", "operator:wikidata": "Q579747", "operator:wikipedia": "en:Guaranty Trust Bank", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.5720783, "lon": -0.2035086, "tags": {"amenity": "atm", "cash_in": "yes", "check_date": "2023-06-05", "currency:GHS": "yes", "drive_through": "no", "indoor": "no", "operator": "Absa", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.5935477, "lon": -0.2209864, "tags": {"amenity": "atm", "cash_in": "no", "check_date": "2024-11-05", "name": "CBG"}}, {"type": "node", "id": **********, "lat": 5.5935345, "lon": -0.2210124, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "brand:wikipedia": "en:GCB Bank", "cash_in": "no", "check_date": "2024-11-07", "name": "GCB Bank", "operator": "GCB Bank", "operator:wikidata": "Q1521346", "operator:wikipedia": "en:GCB Bank"}}, {"type": "node", "id": **********, "lat": 5.6002475, "lon": -0.1879708, "tags": {"amenity": "atm", "cash_in": "yes", "check_date": "2024-11-07", "name": "GCB"}}, {"type": "node", "id": **********, "lat": 5.5874418, "lon": -0.099916, "tags": {"amenity": "atm", "drive_through": "no", "name": "UMB", "operator": "UMB"}}, {"type": "node", "id": **********, "lat": 5.582585, "lon": -0.2084917, "tags": {"amenity": "atm", "operator": "Access bank Ghana"}}, {"type": "node", "id": **********, "lat": 5.6098628, "lon": -0.1831566, "tags": {"amenity": "atm", "brand": "Access Bank", "brand:wikidata": "Q4672420", "cash_in": "yes", "name": "Access Bank", "operator": "Access Bank", "operator:type": "public", "operator:wikidata": "Q4672420", "website": "https://www.ghana.accessbankplc.com/"}}, {"type": "node", "id": **********, "lat": 5.6388537, "lon": -0.209461, "tags": {"amenity": "atm", "name": "UMB"}}, {"type": "node", "id": **********, "lat": 5.6356798, "lon": -0.2147771, "tags": {"amenity": "atm", "brand": "Fidelity Bank", "brand:wikidata": "Q5446778", "brand:wikipedia": "en:Fidelity Bank Ghana", "name": "Fidelity Bank", "operator": "Fidelity Bank", "operator:wikidata": "Q5446778", "operator:wikipedia": "en:Fidelity Bank Ghana"}}, {"type": "node", "id": **********, "lat": 5.5796374, "lon": -0.1554638, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Giffard Road", "addr:suburb": "Burma Camp", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa", "operator:type": "public", "operator:wikidata": "Q58641733", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": **********, "lat": 5.5722104, "lon": -0.2033577, "tags": {"amenity": "atm", "brand": "GT Bank", "brand:wikidata": "Q579747", "cash_in": "yes", "check_date": "2024-01-08", "currency:EUR": "yes", "currency:USD": "yes", "drive_through": "no", "fixme": "exact position", "official_name": "Guaranty Trust Bank", "opening_hours": "Mo-Fr 08:30-16:00; <PERSON><PERSON><PERSON>,PH off", "operator": "GT Bank", "operator:wikidata": "Q579747", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.5629541, "lon": -0.2285526, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "GCB"}}, {"type": "node", "id": **********, "lat": 5.5628883, "lon": -0.2286085, "tags": {"amenity": "atm", "brand": "Fidelity Bank", "brand:wikidata": "Q5446778", "cash_in": "yes", "check_date": "2023-06-05", "currency:GHS": "yes", "drive_through": "no", "opening_hours": "24/7", "operator": "Fidelity Bank", "operator:wikidata": "Q5446778", "source": "local knowledge"}}, {"type": "node", "id": **********, "lat": 5.5851027, "lon": -0.2081516, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "GCB"}}, {"type": "node", "id": **********, "lat": 5.6424479, "lon": -0.1520891, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "First Bank"}}, {"type": "node", "id": **********, "lat": 5.6424305, "lon": -0.1521246, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "SG Ghana"}}, {"type": "node", "id": **********, "lat": 5.6424621, "lon": -0.1520491, "tags": {"amenity": "atm", "opening_hours": "24/7", "operator": "CGB"}}, {"type": "node", "id": **********, "lat": 5.5986827, "lon": -0.1807506, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "<PERSON><PERSON> Road", "addr:suburb": "Airport City", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "cash_in": "yes", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": **********, "lat": 5.6565539, "lon": -0.1815048, "tags": {"amenity": "atm", "brand": "Ecobank", "brand:wikidata": "Q930225", "cash_in": "yes", "currency:GHS": "yes", "indoor": "no", "operator": "Ecobank", "operator:type": "public", "operator:wikidata": "Q930225"}}, {"type": "node", "id": **********, "lat": 5.5677335, "lon": -0.2148393, "tags": {"amenity": "atm", "brand": "CalBank", "brand:wikidata": "Q4035559", "contact:linkedin": "calbank-plc", "contact:twitter": "CalBankPLC", "contact:whatsapp": "+233 24 242 6398", "contact:youtube": "https://www.youtube.com/channel/UCzczU8uI8AFTaXfM-2GDhYQ", "email": "<EMAIL>", "facebook": "CalBankPLC", "name": "CalBank ATM", "operator": "CalBank", "operator:type": "public", "operator:wikidata": "Q4035559", "operator:wikipedia": "en:CalBank", "website": "https://calbank.net"}}, {"type": "node", "id": ***********, "lat": 5.5620709, "lon": -0.1816359, "tags": {"amenity": "atm", "operator": "UBA"}}, {"type": "node", "id": ***********, "lat": 5.6669887, "lon": -0.1861754, "tags": {"amenity": "atm", "currency:GHS": "yes", "drive_through": "no", "operator": "UMB(Universal Merchant Bank)"}}, {"type": "node", "id": ***********, "lat": 5.5751658, "lon": -0.2772137, "tags": {"amenity": "atm", "brand": "Zenith Bank", "brand:wikidata": "Q5978240", "name": "Zenith Bank", "operator": "Zenith Bank", "operator:wikidata": "Q5978240"}}, {"type": "node", "id": ***********, "lat": 5.6669675, "lon": -0.1861759, "tags": {"amenity": "atm", "operator": "GCB (Ghana Commercial Bank)"}}, {"type": "node", "id": ***********, "lat": 5.6669368, "lon": -0.1861759, "tags": {"amenity": "atm", "brand": "Access Bank", "brand:wikidata": "Q4672418", "operator": "Access Bank", "operator:wikidata": "Q4672418"}}, {"type": "node", "id": ***********, "lat": 5.6034747, "lon": -0.1752817, "tags": {"amenity": "atm", "name": "umb"}}, {"type": "node", "id": ***********, "lat": 5.6034745, "lon": -0.1752616, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Airport By-Pass", "addr:suburb": "Airport City", "alt_name": "Standard Chartered Airport Shell ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "Airport Shell Service Statioin, near Airport roundabout, Airport, Accra", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 10623187224, "lat": 5.603475, "lon": -0.1753025, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Airport By-Pass", "addr:suburb": "Airport City", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.6034752, "lon": -0.1753239, "tags": {"amenity": "atm", "name": "Fidelity Bank"}}, {"type": "node", "id": ***********, "lat": 5.5503338, "lon": -0.2035362, "tags": {"amenity": "atm"}}, {"type": "node", "id": ***********, "lat": 5.5454321, "lon": -0.2066077, "tags": {"amenity": "atm"}}, {"type": "node", "id": ***********, "lat": 5.6510275, "lon": -0.1875888, "tags": {"amenity": "atm", "brand": "GCB Bank", "brand:wikidata": "Q1521346", "check_date": "2023-07-25", "description": "An ATM Machine", "internet_access": "no", "name": "GCB Bank ATM", "operator": "GCB Bank", "operator:type": "public", "operator:wikidata": "Q1521346"}}, {"type": "node", "id": ***********, "lat": 5.5791385, "lon": -0.2282593, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Palace Street", "addr:suburb": "North Kaneshie", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.6204162, "lon": -0.2272455, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Achimota Forest Road", "addr:suburb": "<PERSON><PERSON><PERSON><PERSON>", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.6373841, "lon": -0.2169994, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Dome Pillar Two Road", "addr:suburb": "<PERSON><PERSON>", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.6084629, "lon": -0.2094775, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "<PERSON><PERSON><PERSON><PERSON> Obasanjo High Street", "addr:suburb": "<PERSON><PERSON><PERSON><PERSON>", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.6669959, "lon": -0.1869312, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Haatso-Atomic Road", "addr:suburb": "<PERSON><PERSON><PERSON>", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.6027564, "lon": -0.1727852, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:suburb": "Airport City", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "description": "Terminal 3 Arrival Hall, Kotoka International Airport", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.5364943, "lon": -0.2267111, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Harley Street", "addr:suburb": "<PERSON><PERSON>", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.5573549, "lon": -0.1651986, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "La Road", "addr:suburb": "Labadi", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "cash_in": "no", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.5541835, "lon": -0.1977217, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "<PERSON>l <PERSON> Avenue", "addr:suburb": "Ridge", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.5767152, "lon": -0.2542265, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Dr. <PERSON>ia <PERSON>", "addr:suburb": "Darkuman Junction", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "cash_in": "yes", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.616541, "lon": -0.1925799, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Nii Nortey Nyanchi Street", "addr:suburb": "Dzorwulu", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.5976345, "lon": -0.0821728, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Accra-Ashaiman Road", "addr:suburb": "<PERSON><PERSON><PERSON>", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.6133478, "lon": -0.071567, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Accra-Tema Beach Road", "addr:suburb": "<PERSON><PERSON><PERSON>", "amenity": "atm", "brand": "Absa", "brand:wikidata": "Q58641733", "brand:wikipedia": "en:Absa Group Limited", "contact:instagram": "<PERSON><PERSON><PERSON><PERSON>", "contact:linkedin": "absa-ghana", "contact:twitter": "AbsaGhana", "contact:whatsapp": "+233 50 164 4644", "contact:youtube": "https://www.youtube.com/channel/UCKpKPyfJ69tf7lnxudpzKJQ", "email": "<EMAIL>", "facebook": "AbsaGhana", "name": "Absa ATM", "opening_hours": "24/7", "operator": "Absa Bank Ghana Limited", "operator:type": "public", "operator:wikidata": "Q96371413", "operator:wikipedia": "en:Absa Bank Ghana", "phone": "+233 30 242 9150", "website": "https://www.absa.com.gh"}}, {"type": "node", "id": ***********, "lat": 5.5364422, "lon": -0.2267151, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Harley Street", "addr:suburb": "<PERSON><PERSON>", "alt_name": "Standard Chartered Korle Bu ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "Main entrance, Korle Bu Teaching Hospital, Accra", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11373328146, "lat": 5.6061566, "lon": -0.2490968, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "<PERSON>", "addr:suburb": "Lapaz", "alt_name": "Standard Chartered Abeka ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "Meacham House, Opposite Las Palmas Restaurant", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11373329239, "lat": 5.5517541, "lon": -0.2397962, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "<PERSON>", "addr:suburb": "Laterbiokoshie", "alt_name": "Standard Chartered Laterbiokoshie ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11373346841, "lat": 5.5420352, "lon": -0.2644893, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:suburb": "<PERSON><PERSON><PERSON>", "alt_name": "Standard Chartered Dansoman ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11373349245, "lat": 5.5737908, "lon": -0.2793065, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Dr. <PERSON>ia <PERSON>", "addr:suburb": "<PERSON><PERSON>", "alt_name": "Standard Chartered ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "Goil Service Station, Opposite Mallam Market, Accra", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11373361136, "lat": 5.5735552, "lon": -0.2536324, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:suburb": "<PERSON><PERSON><PERSON>", "alt_name": "Standard Chartered Hansonic ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "Hansonic Hotel Entrance, Mataheko, Accra", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11375674915, "lat": 5.5685491, "lon": -0.2220768, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Ring Road Central", "addr:suburb": "North Industrial Area", "alt_name": "Standard Chartered NIA ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "SSNIT Premises, North Industrial Area", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11375681506, "lat": 5.5562084, "lon": -0.2048416, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Liberia Avenue", "addr:suburb": "West Ridge", "alt_name": "Standard Chartered Liberia Road ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "yes", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "Opp. TUC Building, Ridge", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "indoor": "yes", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11375731703, "lat": 5.6146185, "lon": -0.1853292, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "<PERSON><PERSON> Annan Street", "addr:suburb": "Airport Residential Area", "alt_name": "Standard Chartered Nyaho Clinic ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "Nyaho Medical Centre, Airport Residential Area, Accra", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11380245455, "lat": 5.6536936, "lon": -0.2084881, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Westland Avenue", "addr:suburb": "Westland", "alt_name": "Standard Chartered Westland ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "yes", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11380277242, "lat": 5.6508426, "lon": -0.1864967, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Dr. <PERSON><PERSON><PERSON><PERSON>", "addr:suburb": "Legon", "alt_name": "Standard Chartered Legon ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "Near Kingdom Bookshop Legon Campus", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "indoor": "no", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered", "operator:type": "public", "operator:wikidata": "Q548278", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11381761436, "lat": 5.6352271, "lon": -0.1722159, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Lagos Avenue", "addr:suburb": "East Legon", "alt_name": "Standard Chartered East Legon ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "Delino Plaza, Near Mensvic Hotel, Lagos Avenue, East Legon, Accra", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11381911423, "lat": 5.6220605, "lon": -0.1811631, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "George <PERSON>", "addr:suburb": "Airport Residential Area", "alt_name": "Standard Chartered Tullow ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "After Motorway Extension, Accra", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11383547039, "lat": 5.5761326, "lon": -0.1524306, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Giffard Road", "addr:suburb": "La", "alt_name": "Standard Chartered Palmwine Junction ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "Labadi Shell Service Station, Palmwine junction, Accra", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered", "operator:type": "public", "operator:wikidata": "Q548278", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11383576147, "lat": 5.5610684, "lon": -0.1815695, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Oxford Street", "addr:suburb": "<PERSON><PERSON>", "alt_name": "Standard Chartered Osu ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "description": "Ground Floor, Alpha One Building, Opposite Frankies Hotel, Osu Oxford Street", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 261 0750; +233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11383592071, "lat": 5.5710939, "lon": -0.2082521, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Ring Road Central", "addr:suburb": "Asylum Down", "alt_name": "Standard Chartered Ring Road ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "yes", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": 11383604704, "lat": 5.6129308, "lon": -0.0716914, "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:region": "Greater Accra", "addr:street": "Accra-Tema Beach Road", "addr:suburb": "Nungua", "alt_name": "Standard Chartered Junction Mall ATM", "amenity": "atm", "brand": "Standard Chartered", "brand:wikidata": "Q548278", "brand:wikipedia": "en:Standard Chartered", "cash_in": "no", "contact:instagram": "stanchart<PERSON>", "contact:linkedin": "standardchartered", "contact:twitter": "StanChartGH", "email": "<EMAIL>", "facebook": "StandardCharteredGH", "name": "Standard Chartered ATM", "opening_hours": "24/7", "operator": "Standard Chartered Ghana", "operator:type": "public", "operator:wikidata": "Q7598195", "operator:wikipedia": "en:Standard Chartered Ghana", "phone": "+233 30 263 3428; +233 30 274 0100", "website": "https://www.sc.com/gh"}}, {"type": "node", "id": ***********, "lat": 5.5880215, "lon": -0.1832171, "tags": {"amenity": "atm", "check_date": "2023-12-11", "currency:GHS": "yes", "name": "Republic Bank", "opening_hours": "24/7"}}, {"type": "node", "id": ***********, "lat": 5.5880473, "lon": -0.1832585, "tags": {"amenity": "atm", "check_date": "2023-12-11", "currency:GHS": "yes", "name": "CBG", "opening_hours": "24/7"}}, {"type": "node", "id": ***********, "lat": 5.5880648, "lon": -0.183275, "tags": {"amenity": "atm", "brand": "Stanbic Bank", "brand:wikidata": "Q7597999", "check_date": "2023-12-11", "currency:GHS": "yes", "name": "Stanbic Bank", "opening_hours": "24/7", "operator": "Stanbic Bank", "operator:wikidata": "Q7597999"}}, {"type": "node", "id": ***********, "lat": 5.6519751, "lon": -0.1797098, "tags": {"amenity": "atm"}}, {"type": "node", "id": ***********, "lat": 5.5810664, "lon": -0.2166071, "tags": {"amenity": "atm", "cash_in": "no", "name": "<PERSON><PERSON>"}}, {"type": "node", "id": ***********, "lat": 5.5684889, "lon": -0.1741136, "tags": {"amenity": "atm"}}]}