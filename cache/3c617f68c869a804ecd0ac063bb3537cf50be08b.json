{"version": 0.6, "generator": "Overpass API 0.7.62.7 375dc00a", "osm3s": {"timestamp_osm_base": "2025-06-30T13:09:15Z", "copyright": "The data included in this document is from www.openstreetmap.org. The data is made available under ODbL."}, "elements": [{"type": "node", "id": 3037048788, "lat": 5.6113301, "lon": -0.1783673}, {"type": "node", "id": 3105613204, "lat": 5.6082208, "lon": -0.1816839, "tags": {"name": "StarLife Insurance", "office": "insurance"}}, {"type": "node", "id": 3105621513, "lat": 5.6101187, "lon": -0.1821728}, {"type": "node", "id": 3105621514, "lat": 5.6099504, "lon": -0.1821783}, {"type": "node", "id": 3105621515, "lat": 5.6101053, "lon": -0.1819772}, {"type": "node", "id": 3105621516, "lat": 5.6099398, "lon": -0.1819772}, {"type": "node", "id": 3839030769, "lat": 5.5731161, "lon": -0.1973911}, {"type": "node", "id": 3839030770, "lat": 5.5731175, "lon": -0.1975439}, {"type": "node", "id": 3839030771, "lat": 5.5732701, "lon": -0.1973898}, {"type": "node", "id": 3839030772, "lat": 5.5732714, "lon": -0.1975426}, {"type": "node", "id": 4104643037, "lat": 5.5688267, "lon": -0.1836261, "tags": {"name": "SIC Head Office", "office": "insurance"}}, {"type": "node", "id": 4224128022, "lat": 5.6162743, "lon": -0.1800081}, {"type": "node", "id": 4224128023, "lat": 5.6162853, "lon": -0.1797483}, {"type": "node", "id": 4224128024, "lat": 5.6167329, "lon": -0.1797405}, {"type": "node", "id": 4224128025, "lat": 5.6167408, "lon": -0.1800029}, {"type": "node", "id": 4270124402, "lat": 5.6113269, "lon": -0.178155}, {"type": "node", "id": 4270124403, "lat": 5.611331, "lon": -0.1784285}, {"type": "node", "id": 4270124408, "lat": 5.6118436, "lon": -0.1781472}, {"type": "node", "id": 4270124409, "lat": 5.6118476, "lon": -0.1784208}, {"type": "node", "id": 4430018893, "lat": 5.5610672, "lon": -0.2300075, "tags": {"name": "Glico Insurance", "office": "insurance"}}, {"type": "node", "id": 4786654066, "lat": 5.5762705, "lon": -0.2005969}, {"type": "node", "id": 4786654067, "lat": 5.5762324, "lon": -0.2005964}, {"type": "node", "id": 4786654068, "lat": 5.576225, "lon": -0.2006042}, {"type": "node", "id": 4786654235, "lat": 5.5763073, "lon": -0.2005734}, {"type": "node", "id": 4987990752, "lat": 5.5646675, "lon": -0.1869051, "tags": {"name": "Functions", "office": "insurance"}}, {"type": "node", "id": 5022636644, "lat": 5.5447856, "lon": -0.2065898, "tags": {"name": "SIC Life", "office": "insurance"}}, {"type": "node", "id": 5022716845, "lat": 5.5703217, "lon": -0.211574, "tags": {"baby_feeding": "no", "branch": "Accra-Circle", "check_date": "2023-04-28", "name": "Provident Insurane Company", "office": "insurance", "smoking": "no", "source": "survey;local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.5640097, "lon": -0.2275674, "tags": {"air_conditioning": "yes", "baby_feeding": "no", "check_date": "2023-06-05", "internet_access": "yes", "name": "SIC", "office": "insurance", "smoking": "no", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.565231, "lon": -0.1958026, "tags": {"name": "National Health Insurance Authority", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.6302581, "lon": -0.1430901, "tags": {"name": "Enterprise Insurance", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.5651261, "lon": -0.1886161, "tags": {"name": "Phoenix Insurance", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.6082373, "lon": -0.2415918, "tags": {"name": "Star Assurance", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.6288926, "lon": -0.1773023, "tags": {"branch": "Gulf House", "name": "SSNIT", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.6026374, "lon": -0.2005716, "tags": {"name": "Provident Insurance Company", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.5792122, "lon": -0.2283735, "tags": {"name": "Enterprise Insurance", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.5722752, "lon": -0.2058667, "tags": {"name": "Imperial General <PERSON><PERSON><PERSON>", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.6149089, "lon": -0.2112415, "tags": {"name": "National Health Insurance Scheme", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.5604736, "lon": -0.1739749, "tags": {"name": "Midas Insurance Brokers", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.6212141, "lon": -0.173577, "tags": {"brand": "SIC Insurance", "brand:wikidata": "Q7390288", "name": "SIC insurance", "name:it": "Sic", "office": "insurance", "opening_hours": "Mo-Fr 08:00-17:00", "wheelchair": "yes"}}, {"type": "node", "id": **********, "lat": 5.6036205, "lon": -0.1774261, "tags": {"addr:city": "Accra", "addr:street": "Airport by pass road", "air_conditioning": "yes", "alt_name": "SSNIT Emporium Airport City", "baby_feeding": "no", "branch": "Airport City", "check_date": "2023-04-28", "name": "SSNIT", "office": "insurance", "phone": "+233302611622", "smoking": "no", "source": "local knowledge", "website": "https://www.ssnit.org.gh/", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.6095309, "lon": -0.2391064, "tags": {"addr:city": "Accra", "addr:community": "Fly Top", "addr:street": "<PERSON>", "addr:suburb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "SSNIT", "office": "insurance", "opening_hours": "Mo-Fr 09:00-17:00", "source": "Open Cities Accra - Field Survey"}}, {"type": "node", "id": 6293496990, "lat": 5.5689618, "lon": -0.2220024, "tags": {"air_conditioning": "yes", "baby_feeding": "no", "check_date": "2023-05-16", "name": "SSNIT", "office": "insurance", "smoking": "no", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": 6305323804, "lat": 5.5603023, "lon": -0.2103327, "tags": {"name": "Enterprise Life", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.5351859, "lon": -0.2298713, "tags": {"name": "SSNIT", "office": "insurance"}}, {"type": "node", "id": **********, "lat": 5.6110533, "lon": -0.074783, "tags": {"name": "Social Security and National Insurance Trust", "office": "insurance", "short_name": "SSINT", "website": "https://www.ssnit.org.gh/"}}, {"type": "node", "id": **********, "lat": 5.5725344, "lon": -0.2024588, "tags": {"air_conditioning": "yes", "baby_feeding": "no", "check_date": "2023-06-05", "name": "Metropolitan Health Insurance", "office": "insurance", "smoking": "no", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.5761908, "lon": -0.2005723}, {"type": "node", "id": **********, "lat": 5.576181, "lon": -0.2005722}, {"type": "node", "id": **********, "lat": 5.5762708, "lon": -0.200573}, {"type": "node", "id": **********, "lat": 5.5763093, "lon": -0.2004068}, {"type": "node", "id": **********, "lat": 5.576183, "lon": -0.2004053}, {"type": "node", "id": **********, "lat": 5.5761904, "lon": -0.2006038}, {"type": "node", "id": **********, "lat": 5.5770725, "lon": -0.1985887}, {"type": "node", "id": **********, "lat": 5.5771156, "lon": -0.1986726}, {"type": "node", "id": **********, "lat": 5.577071, "lon": -0.1986957}, {"type": "node", "id": **********, "lat": 5.5770279, "lon": -0.1986119}, {"type": "node", "id": **********, "lat": 5.5683139, "lon": -0.19949, "tags": {"name": "Star Assurance", "office": "insurance"}}, {"type": "node", "id": 6746692719, "lat": 5.5687008, "lon": -0.1992695, "tags": {"name": "People's Pension Trust", "office": "insurance"}}, {"type": "node", "id": 6780697050, "lat": 5.6384739, "lon": -0.1251186, "tags": {"name": "Best Assurance", "office": "insurance"}}, {"type": "node", "id": 6790086102, "lat": 5.5970419, "lon": -0.0833971, "tags": {"name": "Dynamic Insurance Brokers Ghana", "office": "insurance"}}, {"type": "node", "id": 6869790063, "lat": 5.5768551, "lon": -0.18117, "tags": {"name": "Donewell Life", "office": "insurance"}}, {"type": "node", "id": 6883039634, "lat": 5.6302431, "lon": -0.1431808, "tags": {"name": "Prudential Life Insurance", "office": "insurance"}}, {"type": "node", "id": 6948060372, "lat": 5.6433247, "lon": -0.113152, "tags": {"name": "Old Mutual", "office": "insurance"}}, {"type": "node", "id": 6983626668, "lat": 5.5853528, "lon": -0.2076702, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "S.C. Nortey Street", "addr:suburb": "New Town", "name": "State Insurance Company Limited", "office": "insurance", "opening_hours": "Mo-Fr 08:00-17:00"}}, {"type": "node", "id": 7207765424, "lat": 5.621211, "lon": -0.2315187, "tags": {"addr:city": "Accra", "name": "Prime insurance", "office": "insurance"}}, {"type": "node", "id": 7274478323, "lat": 5.5634112, "lon": -0.2062769, "tags": {"name": "Activa International Insurance", "office": "insurance"}}, {"type": "node", "id": 8416714717, "lat": 5.5764575, "lon": -0.2092344, "tags": {"name": "SSNIT Kokomlemle branch", "office": "insurance"}}, {"type": "node", "id": 9593797863, "lat": 5.6165595, "lon": -0.1987213, "tags": {"name": "<PERSON><PERSON>", "office": "insurance"}}, {"type": "node", "id": 9594173075, "lat": 5.6119728, "lon": -0.1912934, "tags": {"name": "Petra", "office": "insurance"}}, {"type": "node", "id": 9977853475, "lat": 5.5713235, "lon": -0.1981308, "tags": {"addr:city": "Accra", "addr:housename": "Activa Square", "addr:street": "Houphouet Boigny Road", "air_conditioning": "yes", "baby_feeding": "no", "check_date": "2023-06-23", "contact:facebook": "Activa-Ghana-480713838774562", "contact:instagram": "activainsurance", "contact:linkedin": "https://www.linkedin.com/in/activa-ghana-660378109/", "contact:twitter": "Activa_Ghana", "email": "<EMAIL>", "internet_access": "yes", "name": "Activa International Insurance", "office": "insurance", "opening_hours": "Mo-Fr 08:00-17:00", "phone": "+233 30 268 6352; +233 30 268 5118; +233 30 268 5120", "smoking": "no", "website": "http://www.activa-ghana.com/", "wheelchair": "limited"}}, {"type": "way", "id": 305871611, "nodes": [3105621515, 3105621513, 3105621514, 3105621516, 3105621515], "tags": {"addr:city": "Accra", "addr:housenumber": "47", "addr:street": "<PERSON><PERSON>", "alt_name": "Enterprise Insurance", "branch": "Airport Residential Area", "building": "yes", "name": "Entreprise Group", "office": "insurance", "opening_hours": "Mo-Fr 08:00-16:00"}}, {"type": "way", "id": *********, "nodes": [3839030772, 3839030771, 3839030769, 3839030770, 3839030772], "tags": {"building": "yes", "name": "Unique Insurance Company Limited", "office": "insurance", "opening_hours": "Mo-Fr 08:00-17:00"}}, {"type": "way", "id": *********, "nodes": [4224128025, 4224128024, 4224128023, 4224128022, 4224128025], "tags": {"landuse": "commercial", "name": "ea Equity assurance ltd.", "office": "insurance"}}, {"type": "way", "id": *********, "nodes": [4270124408, 4270124402, 3037048788, 4270124403, 4270124409, 4270124408], "tags": {"landuse": "commercial", "name": "Millenium Insurance", "office": "insurance"}}, {"type": "way", "id": *********, "nodes": [**********, 4786654068, 4786654067, 4786654066, **********, 4786654235, **********, **********, **********, **********, **********], "tags": {"addr:street": "Al-Waleed Bin-Talal Highway", "building": "office", "building:levels": "2", "building:material": "cement_block", "name": "Enterprise Insurance", "office": "insurance", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "way", "id": *********, "nodes": [**********, **********, **********, **********, **********], "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Mallam Futa Street", "addr:suburb": "<PERSON><PERSON>", "brand": "Allianz", "brand:wikidata": "Q487292", "building": "office", "name": "Allianz Insurance", "office": "insurance", "opening_hours": "Mo-Sa 08:00-18:00", "phone": "0244354555", "source": "Open Cities Accra / GARID- Field Survey"}}]}