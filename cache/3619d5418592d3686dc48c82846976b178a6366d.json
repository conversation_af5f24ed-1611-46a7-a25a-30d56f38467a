{"version": 0.6, "generator": "Overpass API 0.7.62.7 375dc00a", "osm3s": {"timestamp_osm_base": "2025-06-30T13:09:15Z", "copyright": "The data included in this document is from www.openstreetmap.org. The data is made available under ODbL."}, "elements": [{"type": "node", "id": **********, "lat": 5.613932, "lon": -0.2098381, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Chiropractic & Wellness Centres"}}, {"type": "node", "id": **********, "lat": 5.5720758, "lon": -0.2025512, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Dua Clinic"}}, {"type": "node", "id": **********, "lat": 5.6015884, "lon": -0.1928943, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Medlab", "opening_hours": "Mo-Fr 07:00-18:00; Sa 09:00-12:00"}}, {"type": "node", "id": **********, "lat": 5.5609961, "lon": -0.1824485, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Ramona Clinic", "name:it": "<PERSON><PERSON>"}}, {"type": "node", "id": **********, "lat": 5.5936846, "lon": -0.1783966, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Police Clinic"}}, {"type": "node", "id": **********, "lat": 5.577304, "lon": -0.2003457}, {"type": "node", "id": **********, "lat": 5.5773083, "lon": -0.1996657}, {"type": "node", "id": **********, "lat": 5.5778356, "lon": -0.2003623}, {"type": "node", "id": **********, "lat": 5.5778368, "lon": -0.1996793}, {"type": "node", "id": **********, "lat": 5.5655821, "lon": -0.1800941}, {"type": "node", "id": **********, "lat": 5.5523517, "lon": -0.2112043, "tags": {"air_conditioning": "yes", "amenity": "clinic", "branch": "Accra-Ghana", "check_date": "2023-04-21", "healthcare": "clinic", "name": "Medical & Dental Council Ghana", "operator:type": "public", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.5543241, "lon": -0.208527, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "SIC Bob Freeman Clinic", "phone": "030268283233"}}, {"type": "node", "id": **********, "lat": 5.6081001, "lon": -0.2420194, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Opmann Clinic"}}, {"type": "node", "id": **********, "lat": 5.539402, "lon": -0.2078364}, {"type": "node", "id": **********, "lat": 5.539521, "lon": -0.2077463}, {"type": "node", "id": **********, "lat": 5.5394752, "lon": -0.2076853}, {"type": "node", "id": **********, "lat": 5.5395458, "lon": -0.2076318}, {"type": "node", "id": **********, "lat": 5.5396397, "lon": -0.207757}, {"type": "node", "id": **********, "lat": 5.5394501, "lon": -0.2079005}, {"type": "node", "id": **********, "lat": 5.5542568, "lon": -0.1718348, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Healthnet Clinic", "name:it": "Healthnet"}}, {"type": "node", "id": **********, "lat": 5.5444776, "lon": -0.2063852, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Club Road Clinic"}}, {"type": "node", "id": **********, "lat": 5.5674737, "lon": -0.2573862, "tags": {"addr:street": "Dansoman Curve Road", "air_conditioning": "yes", "amenity": "clinic", "check_date": "2023-06-10", "healthcare": "clinic", "healthcare:speciality": "general", "name": "Quantum Medical Centre and Eye Clinic", "operator:type": "private", "payment:cash": "yes", "source": "local knowledge", "wheelchair": "limited"}}, {"type": "node", "id": **********, "lat": 5.6136103, "lon": -0.240192, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addr:street": "Dzagble Avenue", "addr:suburb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amenity": "clinic", "dispensing": "yes", "emergency": "no", "healthcare": "clinic", "name": "<PERSON>", "operator": "private_profit", "operator_name": "<PERSON>", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": **********, "lat": 5.5616954, "lon": -0.1725763, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Acheampong Specialist Clinic"}}, {"type": "node", "id": **********, "lat": 5.5690457, "lon": -0.1475528, "tags": {"amenity": "clinic", "healthcare": "clinic"}}, {"type": "node", "id": **********, "lat": 5.6188111, "lon": -0.0909657, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Greenfield", "phone": "**********", "website": "https://greenfieldherbal.com/"}}, {"type": "node", "id": **********, "lat": 5.6243857, "lon": -0.0909543, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "<PERSON>fan <PERSON> Clinic", "phone": "**********"}}, {"type": "node", "id": **********, "lat": 5.5771666, "lon": -0.1715493}, {"type": "node", "id": **********, "lat": 5.57731, "lon": -0.1715633}, {"type": "node", "id": **********, "lat": 5.5771922, "lon": -0.1716387}, {"type": "node", "id": **********, "lat": 5.5772881, "lon": -0.171648}, {"type": "node", "id": **********, "lat": 5.5771779, "lon": -0.1714328}, {"type": "node", "id": **********, "lat": 5.5773212, "lon": -0.1714468}, {"type": "node", "id": **********, "lat": 5.5771314, "lon": -0.1716328}, {"type": "node", "id": **********, "lat": 5.5771397, "lon": -0.1715467}, {"type": "node", "id": **********, "lat": 5.5773369, "lon": -0.1715659}, {"type": "node", "id": **********, "lat": 5.5773286, "lon": -0.171652}, {"type": "node", "id": **********, "lat": 5.5771878, "lon": -0.1716839}, {"type": "node", "id": **********, "lat": 5.5772837, "lon": -0.1716933}, {"type": "node", "id": **********, "lat": 5.5827954, "lon": -0.2067492}, {"type": "node", "id": **********, "lat": 5.5827954, "lon": -0.2067466}, {"type": "node", "id": **********, "lat": 5.5827399, "lon": -0.2068951}, {"type": "node", "id": **********, "lat": 5.5827383, "lon": -0.2067499}, {"type": "node", "id": **********, "lat": 5.5828454, "lon": -0.206746}, {"type": "node", "id": **********, "lat": 5.5828471, "lon": -0.2068939}, {"type": "node", "id": **********, "lat": 5.5755888, "lon": -0.1941216, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "GBC Clinic"}}, {"type": "node", "id": **********, "lat": 5.5831111, "lon": -0.2059525}, {"type": "node", "id": **********, "lat": 5.5831114, "lon": -0.2059457}, {"type": "node", "id": **********, "lat": 5.5831313, "lon": -0.2059533}, {"type": "node", "id": **********, "lat": 5.5829753, "lon": -0.2059414}, {"type": "node", "id": **********, "lat": 5.5829716, "lon": -0.2059823}, {"type": "node", "id": **********, "lat": 5.5831306, "lon": -0.2059804}, {"type": "node", "id": **********, "lat": 5.5796737, "lon": -0.1969492}, {"type": "node", "id": **********, "lat": 5.5796664, "lon": -0.1969317}, {"type": "node", "id": **********, "lat": 5.5796224, "lon": -0.1969502}, {"type": "node", "id": **********, "lat": 5.5796297, "lon": -0.1969677}, {"type": "node", "id": **********, "lat": 5.5796504, "lon": -0.1970174}, {"type": "node", "id": **********, "lat": 5.579599, "lon": -0.196894}, {"type": "node", "id": **********, "lat": 5.5797084, "lon": -0.196848}, {"type": "node", "id": **********, "lat": 5.5797598, "lon": -0.1969714}, {"type": "node", "id": **********, "lat": 5.6264187, "lon": -0.1537796, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Medical & Dental Clini6"}}, {"type": "node", "id": **********, "lat": 5.608267, "lon": -0.1709184, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Airport Clinic Limited"}}, {"type": "node", "id": **********, "lat": 5.6074867, "lon": -0.1712902, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Airport Clinic Limited"}}, {"type": "node", "id": **********, "lat": 5.5824186, "lon": -0.1983361, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Al-Waleed Bin-Talal Highway", "addr:suburb": "Bo<PERSON><PERSON>", "amenity": "clinic", "healthcare": "clinic", "name": "Dons Medical Services", "opening_hours": "Mo-Sa 08:00-18:00", "operator": "private_profit", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": **********, "lat": 5.5823822, "lon": -0.1992897, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Yahaya Seidu Street", "addr:suburb": "Bo<PERSON><PERSON>", "amenity": "clinic", "dispensing": "yes", "emergency": "yes", "healthcare": "clinic", "name": "New Cross Clinic and Medical", "opening_hours": "24/7", "operator": "private_profit", "phone": "**********", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": **********, "lat": 5.5820843, "lon": -0.2012944, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "<PERSON><PERSON><PERSON>", "amenity": "clinic", "bed_count": "2", "dispensing": "no", "emergency": "no", "healthcare": "clinic", "name": "Vision Clinic", "opening_hours": "Mo-Fr 09:00-17:00", "operator": "private_np", "operator_name": "<PERSON><PERSON><PERSON>", "phone": "+233243365994", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": **********, "lat": 5.5904306, "lon": -0.2038729, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "<PERSON><PERSON><PERSON><PERSON>", "addr:suburb": "Kotobabi Police Station", "amenity": "clinic", "dispensing": "yes", "emergency": "yes", "healthcare": "clinic", "name": "Royal Star Clinic and Diagnostic Center", "opening_hours": "Mo-Su 09:00-21:00", "operator": "private_profit", "operator_name": "Royal Star Clinic", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": **********, "lat": 5.5815693, "lon": -0.1973069, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Mallam Futa Street", "addr:suburb": "Nima Market", "amenity": "clinic", "dispensing": "yes", "healthcare": "clinic", "name": "Layfa Clinic", "opening_hours": "24/7", "operator": "private_profit", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": **********, "lat": 5.5814972, "lon": -0.201922, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Reverend <PERSON>", "addr:suburb": "Sunset", "amenity": "clinic", "healthcare": "clinic", "name": "Sina Zuwina Herbal Center", "opening_hours": "Mo-Sa 09:00-17:00", "phone": "+233208948821", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": **********, "lat": 5.5541261, "lon": -0.2085015, "tags": {"amenity": "clinic", "healthcare": "clinic"}}, {"type": "node", "id": **********, "lat": 5.5832245, "lon": -0.2077552, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Odonko Street", "addr:suburb": "Freedom", "amenity": "clinic", "emergency": "yes", "healthcare": "clinic", "name": "Zak-med Clinic", "operator": "private_profit", "phone": "+233244179253;+233504179253", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": **********, "lat": 5.5916186, "lon": -0.2045513, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Alajo High Street", "addr:suburb": "New Town", "amenity": "clinic", "emergency": "yes", "healthcare": "clinic", "name": "Lifeview Medical Laboratory and Ultra Scan centre", "operator": "private_profit", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": **********, "lat": 5.6078705, "lon": -0.1275719, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Givers Scientic Herbal Clinic"}}, {"type": "node", "id": **********, "lat": 5.6207118, "lon": -0.2255232, "tags": {"amenity": "clinic", "healthcare": "clinic"}}, {"type": "node", "id": **********, "lat": 5.5660003, "lon": -0.2103476, "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "<PERSON><PERSON><PERSON>"}}, {"type": "node", "id": **********, "lat": 5.5771803, "lon": -0.2137475, "tags": {"addr:city": "Accra", "addr:street": "Homowo Ave", "alt_name": "Marie <PERSON> Clinic", "amenity": "clinic", "branch": "Kokomlemle Centre", "contact:phone": "+233 80 020 8585", "healthcare": "clinic", "name": "<PERSON>", "opening_hours": "Mo-Fr 07:00-16:00; Sa 07:30-13:00; Su off; PH off", "website": "https://www.mariestopes.org.gh/find-us/clinics/kokomlemle-centre/"}}, {"type": "node", "id": **********, "lat": 5.5656824, "lon": -0.179967}, {"type": "node", "id": **********, "lat": 5.5655518, "lon": -0.1800374}, {"type": "node", "id": **********, "lat": 5.5656202, "lon": -0.1801655}, {"type": "node", "id": **********, "lat": 5.5657508, "lon": -0.180095}, {"type": "node", "id": 11168489914, "lat": 5.5985371, "lon": -0.1988832, "tags": {"addr:street": "Panfo Street", "amenity": "clinic", "healthcare": "clinic", "name": "EFAN VICTORY CLINIC"}}, {"type": "node", "id": 12025133499, "lat": 5.6011106, "lon": -0.1205109, "tags": {"amenity": "clinic", "check_date": "2024-07-01", "healthcare": "clinic", "name": "Celestial Medicare Clinic", "phone": "+233 24 143 1093"}}, {"type": "node", "id": 12718397455, "lat": 5.5793996, "lon": -0.1445421}, {"type": "node", "id": 12718397456, "lat": 5.5794236, "lon": -0.1445544}, {"type": "node", "id": 12718397457, "lat": 5.5794389, "lon": -0.1444881}, {"type": "node", "id": 12718397458, "lat": 5.5794535, "lon": -0.1444955}, {"type": "node", "id": 12718397459, "lat": 5.5793993, "lon": -0.1443907}, {"type": "node", "id": 12718397460, "lat": 5.5793882, "lon": -0.1444126}, {"type": "node", "id": 12718397461, "lat": 5.5792646, "lon": -0.1444243}, {"type": "node", "id": 12718397462, "lat": 5.5793014, "lon": -0.1444432}, {"type": "node", "id": 12718397463, "lat": 5.5792562, "lon": -0.1445321}, {"type": "node", "id": 12718397464, "lat": 5.5792127, "lon": -0.1445097}, {"type": "node", "id": 12718397465, "lat": 5.5793099, "lon": -0.144596}, {"type": "node", "id": 12718397466, "lat": 5.5793024, "lon": -0.1446108}, {"type": "node", "id": 12718397467, "lat": 5.5793593, "lon": -0.1446214}, {"type": "node", "id": 12718397468, "lat": 5.5794589, "lon": -0.1444488}, {"type": "node", "id": 12718397469, "lat": 5.579306, "lon": -0.1443429}, {"type": "node", "id": 12718397470, "lat": 5.5791905, "lon": -0.1445535}, {"type": "way", "id": 381731531, "nodes": [**********, **********, **********, **********, **********], "tags": {"amenity": "clinic", "healthcare": "clinic", "name": "Nima Goverment Clinic"}}, {"type": "way", "id": 546664075, "nodes": [**********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "clinic", "building": "yes", "building:levels": "2", "healthcare": "clinic", "name": "Cathedral Clinic and Lab Limited", "roof:shape": "hipped", "source": "<PERSON>"}}, {"type": "way", "id": 702226170, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "clinic", "building": "yes", "healthcare": "clinic", "name": "Akai House Clinic"}}, {"type": "way", "id": 706230762, "nodes": [**********, **********, **********, **********, **********, **********, **********], "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Chief <PERSON><PERSON>", "addr:suburb": "Angola", "amenity": "clinic", "building": "residential", "building:levels": "2", "building:material": "cement_block", "dispensing": "yes", "emergency": "yes", "healthcare": "clinic", "name": "Ghanuba Specialist Clinic", "opening_hours": "Mo-Fr 08:00-18:00; Sa-Su 08:00-15:00", "operator": "private_profit", "roof:material": "concrete", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "way", "id": 707847400, "nodes": [**********, **********, **********, **********, **********, **********, **********], "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Chief <PERSON><PERSON>", "addr:suburb": "New Town", "amenity": "clinic", "bed_count": "1", "building": "hospital", "building:levels": "1", "building:material": "cement_block", "dispensing": "no", "emergency": "no", "healthcare": "clinic", "name": "Accra New Town CHPS Compound", "operator": "Ghana Health Service", "operator:type": "public", "operator:wikidata": "Q5555350", "roof:material": "metal", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "way", "id": 708086101, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, **********], "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "<PERSON><PERSON>ya <PERSON>", "addr:suburb": "<PERSON><PERSON>", "amenity": "clinic", "building": "clinic", "building:levels": "1", "building:material": "cement_block", "description": "clinic", "healthcare": "clinic", "name": "Daprof Clinic", "opening_hours": "24/7", "phone": "+233243375577", "roof:material": "concrete", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "way", "id": 882483445, "nodes": [**********, **********, **********, **********, **********, **********], "tags": {"amenity": "clinic", "branch": "<PERSON><PERSON>", "building": "yes", "healthcare": "clinic", "name": "Rabito Clinic", "opening_hours": "Mo-Fr 08:00-19:00", "website": "https://www.rabitoclinic.com/"}}, {"type": "way", "id": **********, "nodes": [12718397470, 12718397466, 12718397465, 12718397467, 12718397455, 12718397456, 12718397458, 12718397457, 12718397468, 12718397460, 12718397459, 12718397469, 12718397461, 12718397462, 12718397463, 12718397464, 12718397470], "tags": {"amenity": "clinic", "building": "hospital", "healthcare": "clinic", "name": "Bright ALK Herbal Clinic"}}]}