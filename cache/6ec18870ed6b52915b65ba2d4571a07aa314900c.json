{"version": 0.6, "generator": "Overpass API 0.7.62.7 375dc00a", "osm3s": {"timestamp_osm_base": "2025-06-30T13:09:15Z", "copyright": "The data included in this document is from www.openstreetmap.org. The data is made available under ODbL."}, "elements": [{"type": "node", "id": 107552545, "lat": 5.5894356, "lon": -0.1848755}, {"type": "node", "id": **********, "lat": 5.6354204, "lon": -0.2153604, "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Lapaz Community Hospital", "operator": "Lapaz Community Hospital"}}, {"type": "node", "id": **********, "lat": 5.5675273, "lon": -0.1814043}, {"type": "node", "id": **********, "lat": 5.5675899, "lon": -0.1809369}, {"type": "node", "id": **********, "lat": 5.5677439, "lon": -0.1812955}, {"type": "node", "id": **********, "lat": 5.5681039, "lon": -0.1825108}, {"type": "node", "id": **********, "lat": 5.5682899, "lon": -0.1827606}, {"type": "node", "id": **********, "lat": 5.5683204, "lon": -0.1807937}, {"type": "node", "id": **********, "lat": 5.5689885, "lon": -0.1812235}, {"type": "node", "id": **********, "lat": 5.5690983, "lon": -0.1820081}, {"type": "node", "id": **********, "lat": 5.5692067, "lon": -0.1806614}, {"type": "node", "id": **********, "lat": 5.5692371, "lon": -0.1822748}, {"type": "node", "id": **********, "lat": 5.5692886, "lon": -0.1811664}, {"type": "node", "id": **********, "lat": 5.56935, "lon": -0.1818411}, {"type": "node", "id": **********, "lat": 5.65069, "lon": -0.1774525}, {"type": "node", "id": **********, "lat": 5.6507092, "lon": -0.178778}, {"type": "node", "id": **********, "lat": 5.6527401, "lon": -0.1774359}, {"type": "node", "id": **********, "lat": 5.6527593, "lon": -0.1787283}, {"type": "node", "id": **********, "lat": 5.613695, "lon": -0.1854153}, {"type": "node", "id": **********, "lat": 5.6143325, "lon": -0.1858652}, {"type": "node", "id": **********, "lat": 5.6143692, "lon": -0.1841533}, {"type": "node", "id": **********, "lat": 5.614588, "lon": -0.1854547}, {"type": "node", "id": **********, "lat": 5.6147636, "lon": -0.1851724}, {"type": "node", "id": **********, "lat": 5.6154172, "lon": -0.1841222}, {"type": "node", "id": **********, "lat": 5.6087285, "lon": -0.2534225}, {"type": "node", "id": **********, "lat": 5.6087724, "lon": -0.2540429}, {"type": "node", "id": **********, "lat": 5.6086459, "lon": -0.2537496}, {"type": "node", "id": **********, "lat": 5.6085398, "lon": -0.2534863}, {"type": "node", "id": **********, "lat": 5.6089935, "lon": -0.2539928}, {"type": "node", "id": **********, "lat": 5.5838331, "lon": -0.1864536}, {"type": "node", "id": **********, "lat": 5.5845263, "lon": -0.1858701}, {"type": "node", "id": **********, "lat": 5.5847501, "lon": -0.1861412}, {"type": "node", "id": **********, "lat": 5.5858549, "lon": -0.1863502}, {"type": "node", "id": **********, "lat": 5.5509404, "lon": -0.1960157}, {"type": "node", "id": **********, "lat": 5.5509537, "lon": -0.1958352}, {"type": "node", "id": **********, "lat": 5.5509786, "lon": -0.1962082}, {"type": "node", "id": **********, "lat": 5.5509875, "lon": -0.1960063}, {"type": "node", "id": **********, "lat": 5.5510674, "lon": -0.1958125}, {"type": "node", "id": **********, "lat": 5.5511394, "lon": -0.1961761}, {"type": "node", "id": **********, "lat": 5.6291955, "lon": -0.1863331}, {"type": "node", "id": **********, "lat": 5.63172, "lon": -0.18385}, {"type": "node", "id": **********, "lat": 5.6328424, "lon": -0.1883796}, {"type": "node", "id": **********, "lat": 5.6345321, "lon": -0.1854218}, {"type": "node", "id": **********, "lat": 5.635789, "lon": -0.1612372}, {"type": "node", "id": **********, "lat": 5.552038, "lon": -0.2642063}, {"type": "node", "id": **********, "lat": 5.5521365, "lon": -0.2623398}, {"type": "node", "id": **********, "lat": 5.5561062, "lon": -0.1670641}, {"type": "node", "id": **********, "lat": 5.5969694, "lon": -0.1123642}, {"type": "node", "id": **********, "lat": 5.5970086, "lon": -0.1122808}, {"type": "node", "id": **********, "lat": 5.5970449, "lon": -0.112298}, {"type": "node", "id": **********, "lat": 5.5970342, "lon": -0.1123207}, {"type": "node", "id": **********, "lat": 5.5970516, "lon": -0.1123289}, {"type": "node", "id": **********, "lat": 5.5970617, "lon": -0.1123074}, {"type": "node", "id": **********, "lat": 5.5970875, "lon": -0.1123196}, {"type": "node", "id": **********, "lat": 5.5706976, "lon": -0.1764129}, {"type": "node", "id": **********, "lat": 5.5624, "lon": -0.1819034}, {"type": "node", "id": **********, "lat": 5.5626236, "lon": -0.1818639}, {"type": "node", "id": **********, "lat": 5.5628071, "lon": -0.1822883}, {"type": "node", "id": **********, "lat": 5.5551447, "lon": -0.1791047}, {"type": "node", "id": **********, "lat": 5.5550232, "lon": -0.1794469}, {"type": "node", "id": **********, "lat": 5.5551383, "lon": -0.1794882}, {"type": "node", "id": **********, "lat": 5.5551945, "lon": -0.17933}, {"type": "node", "id": **********, "lat": 5.5552145, "lon": -0.1793371}, {"type": "node", "id": **********, "lat": 5.555233, "lon": -0.179285}, {"type": "node", "id": **********, "lat": 5.5552241, "lon": -0.1792819}, {"type": "node", "id": **********, "lat": 5.5552314, "lon": -0.1792613}, {"type": "node", "id": **********, "lat": 5.5552543, "lon": -0.1791971}, {"type": "node", "id": **********, "lat": 5.5792493, "lon": -0.2451579}, {"type": "node", "id": **********, "lat": 5.5514896, "lon": -0.2039394, "tags": {"amenity": "hospital", "healthcare": "hospital", "level": "1", "name": "Nyaho Medical Centre"}}, {"type": "node", "id": **********, "lat": 5.5792805, "lon": -0.2450914}, {"type": "node", "id": **********, "lat": 5.5792602, "lon": -0.2450567}, {"type": "node", "id": **********, "lat": 5.5792081, "lon": -0.2450875}, {"type": "node", "id": **********, "lat": 5.5792284, "lon": -0.2451222}, {"type": "node", "id": **********, "lat": 5.579326, "lon": -0.2450105}, {"type": "node", "id": **********, "lat": 5.5792786, "lon": -0.244941}, {"type": "node", "id": **********, "lat": 5.579357, "lon": -0.2449922}, {"type": "node", "id": **********, "lat": 5.5791625, "lon": -0.2450096}, {"type": "node", "id": **********, "lat": 5.5792836, "lon": -0.244938}, {"type": "node", "id": **********, "lat": 5.5794014, "lon": -0.2450681}, {"type": "node", "id": **********, "lat": 5.637384, "lon": -0.1648464}, {"type": "node", "id": **********, "lat": 5.6138708, "lon": -0.2404309}, {"type": "node", "id": **********, "lat": 5.6138642, "lon": -0.2404583}, {"type": "node", "id": **********, "lat": 5.6138335, "lon": -0.2404486}, {"type": "node", "id": **********, "lat": 5.6137496, "lon": -0.2404264}, {"type": "node", "id": **********, "lat": 5.6137889, "lon": -0.2402765}, {"type": "node", "id": **********, "lat": 5.6139242, "lon": -0.2403123}, {"type": "node", "id": **********, "lat": 5.6139065, "lon": -0.24038}, {"type": "node", "id": **********, "lat": 5.6320432, "lon": -0.1523594}, {"type": "node", "id": **********, "lat": 5.6068096, "lon": -0.2377081}, {"type": "node", "id": **********, "lat": 5.6138856, "lon": -0.2403744}, {"type": "node", "id": **********, "lat": 5.5970488, "lon": -0.1124019}, {"type": "node", "id": **********, "lat": 5.6127695, "lon": -0.1982026}, {"type": "node", "id": **********, "lat": 5.5586026, "lon": -0.2435214}, {"type": "node", "id": **********, "lat": 5.555271, "lon": -0.1791499}, {"type": "node", "id": **********, "lat": 5.5852497, "lon": -0.1994799}, {"type": "node", "id": **********, "lat": 5.5852389, "lon": -0.1994103}, {"type": "node", "id": **********, "lat": 5.585266, "lon": -0.1994777}, {"type": "node", "id": **********, "lat": 5.5852758, "lon": -0.1995699}, {"type": "node", "id": **********, "lat": 5.5852506, "lon": -0.1994085}, {"type": "node", "id": **********, "lat": 5.5853529, "lon": -0.199391}, {"type": "node", "id": **********, "lat": 5.583396, "lon": -0.1611712}, {"type": "node", "id": **********, "lat": 5.5851314, "lon": -0.162523}, {"type": "node", "id": **********, "lat": 5.5853711, "lon": -0.1995584}, {"type": "node", "id": **********, "lat": 5.5624191, "lon": -0.1820199}, {"type": "node", "id": **********, "lat": 5.562315, "lon": -0.1820358}, {"type": "node", "id": **********, "lat": 5.5623817, "lon": -0.1823611}, {"type": "node", "id": **********, "lat": 5.6150576, "lon": -0.1986275}, {"type": "node", "id": 7346459153, "lat": 5.6146143, "lon": -0.1981963}, {"type": "node", "id": 7346459154, "lat": 5.6149941, "lon": -0.1978261}, {"type": "node", "id": 7346459155, "lat": 5.6154998, "lon": -0.198048}, {"type": "node", "id": 7346459156, "lat": 5.6153395, "lon": -0.1983002}, {"type": "node", "id": 7346764594, "lat": 5.612627, "lon": -0.2142334}, {"type": "node", "id": 7346764595, "lat": 5.6123816, "lon": -0.214645}, {"type": "node", "id": 7346764596, "lat": 5.6120985, "lon": -0.2144914}, {"type": "node", "id": 7346764597, "lat": 5.6123552, "lon": -0.2140513}, {"type": "node", "id": 7346780723, "lat": 5.5739253, "lon": -0.1123436}, {"type": "node", "id": 7346780724, "lat": 5.5730551, "lon": -0.1119472}, {"type": "node", "id": 7346780725, "lat": 5.5724831, "lon": -0.1116968}, {"type": "node", "id": 7346780726, "lat": 5.5720407, "lon": -0.1115255}, {"type": "node", "id": 7346780727, "lat": 5.5718005, "lon": -0.1122497}, {"type": "node", "id": 7346780728, "lat": 5.5725693, "lon": -0.1125608}, {"type": "node", "id": 7346780729, "lat": 5.5728736, "lon": -0.1126788}, {"type": "node", "id": 7346780730, "lat": 5.5731352, "lon": -0.112711}, {"type": "node", "id": 7346780731, "lat": 5.5735517, "lon": -0.1128827}, {"type": "node", "id": 7346795601, "lat": 5.6041006, "lon": -0.1199093}, {"type": "node", "id": 7346795602, "lat": 5.6035758, "lon": -0.1206907}, {"type": "node", "id": 7346795603, "lat": 5.6031912, "lon": -0.1204685}, {"type": "node", "id": 7346795604, "lat": 5.6030064, "lon": -0.1208257}, {"type": "node", "id": 7346795605, "lat": 5.6021408, "lon": -0.1204332}, {"type": "node", "id": 7346795606, "lat": 5.6028472, "lon": -0.1192}, {"type": "node", "id": 7346824065, "lat": 5.6196196, "lon": -0.1478994}, {"type": "node", "id": 7346824066, "lat": 5.6193289, "lon": -0.1478923}, {"type": "node", "id": 7346824067, "lat": 5.6193154, "lon": -0.1472968}, {"type": "node", "id": 7346824068, "lat": 5.6192929, "lon": -0.1471435}, {"type": "node", "id": 7346824069, "lat": 5.6192121, "lon": -0.1468006}, {"type": "node", "id": 7346824070, "lat": 5.6190954, "lon": -0.1464578}, {"type": "node", "id": 7346824071, "lat": 5.6189832, "lon": -0.1461781}, {"type": "node", "id": 7346824072, "lat": 5.6190371, "lon": -0.1459661}, {"type": "node", "id": 7346824073, "lat": 5.619257, "lon": -0.1457361}, {"type": "node", "id": 7346824074, "lat": 5.6193558, "lon": -0.1456052}, {"type": "node", "id": 7346824075, "lat": 5.6193199, "lon": -0.1451316}, {"type": "node", "id": 7346824076, "lat": 5.6196341, "lon": -0.1450414}, {"type": "node", "id": 7350120722, "lat": 5.5859503, "lon": -0.1842705}, {"type": "node", "id": 7350120723, "lat": 5.587689, "lon": -0.1861965}, {"type": "node", "id": 7350120724, "lat": 5.5866431, "lon": -0.183964}, {"type": "node", "id": 7350120725, "lat": 5.5871412, "lon": -0.183606}, {"type": "node", "id": 7350120726, "lat": 5.5890084, "lon": -0.1823445}, {"type": "node", "id": 7350120727, "lat": 5.588214, "lon": -0.1828608}, {"type": "node", "id": 7350897428, "lat": 5.5622074, "lon": -0.2053328}, {"type": "node", "id": 7350897429, "lat": 5.5616776, "lon": -0.2052561}, {"type": "node", "id": 7350897430, "lat": 5.5611927, "lon": -0.2051298}, {"type": "node", "id": 7350897431, "lat": 5.5610176, "lon": -0.2049043}, {"type": "node", "id": 7350897432, "lat": 5.5609952, "lon": -0.2047103}, {"type": "node", "id": 7350897433, "lat": 5.5619874, "lon": -0.2035826}, {"type": "node", "id": 7350897434, "lat": 5.5620817, "lon": -0.2036638}, {"type": "node", "id": 7350897435, "lat": 5.5625172, "lon": -0.2035285}, {"type": "node", "id": 7350897436, "lat": 5.5626474, "lon": -0.2036006}, {"type": "node", "id": 7350897437, "lat": 5.5623915, "lon": -0.2046562}, {"type": "node", "id": 7350897438, "lat": 5.5623915, "lon": -0.2048366}, {"type": "node", "id": 7352245782, "lat": 5.571805, "lon": -0.2371918}, {"type": "node", "id": 7352245783, "lat": 5.5722504, "lon": -0.2356139}, {"type": "node", "id": 7352245784, "lat": 5.5718238, "lon": -0.2354735}, {"type": "node", "id": 7352248049, "lat": 5.6307652, "lon": -0.1685357}, {"type": "node", "id": 7352248050, "lat": 5.6307613, "lon": -0.1688613}, {"type": "node", "id": 7352248051, "lat": 5.631284, "lon": -0.1688676}, {"type": "node", "id": 7352248052, "lat": 5.6312879, "lon": -0.168542}, {"type": "node", "id": 7352314985, "lat": 5.571771, "lon": -0.235519}, {"type": "node", "id": 7352314986, "lat": 5.5717068, "lon": -0.2357656}, {"type": "node", "id": 7352314987, "lat": 5.5713444, "lon": -0.2370667}, {"type": "node", "id": 7354126353, "lat": 5.5494917, "lon": -0.2412892}, {"type": "node", "id": 7354126354, "lat": 5.549036, "lon": -0.2412774}, {"type": "node", "id": 7354126355, "lat": 5.5490487, "lon": -0.2409489}, {"type": "node", "id": 7354126356, "lat": 5.5494868, "lon": -0.2409489}, {"type": "node", "id": **********, "lat": 5.5935588, "lon": -0.2112278}, {"type": "node", "id": **********, "lat": 5.5935481, "lon": -0.2116838}, {"type": "node", "id": **********, "lat": 5.5938545, "lon": -0.2115874}, {"type": "node", "id": **********, "lat": 5.593764, "lon": -0.2112968}, {"type": "node", "id": **********, "lat": 5.5936237, "lon": -0.211341}, {"type": "node", "id": **********, "lat": 5.5935858, "lon": -0.2112193}, {"type": "node", "id": **********, "lat": 5.5689158, "lon": -0.1860556}, {"type": "node", "id": **********, "lat": 5.5686168, "lon": -0.1862138}, {"type": "node", "id": **********, "lat": 5.5685741, "lon": -0.1861521}, {"type": "node", "id": **********, "lat": 5.5685207, "lon": -0.1858651}, {"type": "node", "id": **********, "lat": 5.5688971, "lon": -0.1858812}, {"type": "node", "id": **********, "lat": 5.6353776, "lon": -0.161193}, {"type": "node", "id": **********, "lat": 5.6354148, "lon": -0.1608428}, {"type": "node", "id": **********, "lat": 5.6358263, "lon": -0.160887}, {"type": "node", "id": **********, "lat": 5.5516897, "lon": -0.2642029}, {"type": "node", "id": **********, "lat": 5.5516872, "lon": -0.2644746}, {"type": "node", "id": **********, "lat": 5.5520355, "lon": -0.2644777}, {"type": "node", "id": **********, "lat": 5.5524216, "lon": -0.2623161}, {"type": "node", "id": **********, "lat": 5.5524182, "lon": -0.2621131}, {"type": "node", "id": **********, "lat": 5.5521264, "lon": -0.2621187}, {"type": "node", "id": **********, "lat": 5.5544715, "lon": -0.1667341}, {"type": "node", "id": **********, "lat": 5.5547773, "lon": -0.1659261}, {"type": "node", "id": **********, "lat": 5.5549019, "lon": -0.1659602}, {"type": "node", "id": **********, "lat": 5.5563252, "lon": -0.1664913}, {"type": "node", "id": **********, "lat": 5.5704547, "lon": -0.1766435}, {"type": "node", "id": **********, "lat": 5.5705268, "lon": -0.1769064}, {"type": "node", "id": **********, "lat": 5.5701904, "lon": -0.1769493}, {"type": "node", "id": **********, "lat": 5.5701744, "lon": -0.1765362}, {"type": "node", "id": **********, "lat": 5.570412, "lon": -0.1765389}, {"type": "node", "id": **********, "lat": 5.5704467, "lon": -0.1765738}, {"type": "node", "id": **********, "lat": 5.5706549, "lon": -0.176378}, {"type": "node", "id": **********, "lat": 5.6374094, "lon": -0.1645019}, {"type": "node", "id": **********, "lat": 5.6369888, "lon": -0.1644572}, {"type": "node", "id": **********, "lat": 5.6369554, "lon": -0.1647985}, {"type": "node", "id": **********, "lat": 5.6323752, "lon": -0.15251}, {"type": "node", "id": **********, "lat": 5.632535, "lon": -0.1521543}, {"type": "node", "id": **********, "lat": 5.632203, "lon": -0.1520037}, {"type": "node", "id": **********, "lat": 5.606281, "lon": -0.2378086}, {"type": "node", "id": **********, "lat": 5.6062572, "lon": -0.2374274}, {"type": "node", "id": **********, "lat": 5.6067619, "lon": -0.2373668}, {"type": "node", "id": **********, "lat": 5.6125965, "lon": -0.1980479}, {"type": "node", "id": **********, "lat": 5.6124394, "lon": -0.1982106}, {"type": "node", "id": **********, "lat": 5.6126013, "lon": -0.1983765}, {"type": "node", "id": **********, "lat": 5.5583036, "lon": -0.2435965}, {"type": "node", "id": **********, "lat": 5.5582048, "lon": -0.2432076}, {"type": "node", "id": **********, "lat": 5.5585118, "lon": -0.2431379}, {"type": "node", "id": **********, "lat": 5.5837036, "lon": -0.1608555}, {"type": "node", "id": **********, "lat": 5.5856653, "lon": -0.161949}, {"type": "node", "id": **********, "lat": 5.5851687, "lon": -0.1614769}, {"type": "node", "id": **********, "lat": 5.5845921, "lon": -0.1620563}, {"type": "node", "id": **********, "lat": 5.5834679, "lon": -0.1604946}, {"type": "node", "id": **********, "lat": 5.5832456, "lon": -0.1604991}, {"type": "node", "id": **********, "lat": 5.5831873, "lon": -0.1605442}, {"type": "node", "id": **********, "lat": 5.5831401, "lon": -0.1605532}, {"type": "node", "id": **********, "lat": 5.5830908, "lon": -0.1606344}, {"type": "node", "id": **********, "lat": 5.5829336, "lon": -0.1607833}, {"type": "node", "id": **********, "lat": 5.5964844, "lon": -0.2168298, "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Citadel Hospital"}}, {"type": "node", "id": **********, "lat": 5.5613497, "lon": -0.2051654}, {"type": "node", "id": **********, "lat": 5.556284, "lon": -0.1665992}, {"type": "node", "id": **********, "lat": 5.54247, "lon": -0.2293345}, {"type": "node", "id": **********, "lat": 5.5423221, "lon": -0.2294301}, {"type": "node", "id": **********, "lat": 5.5422215, "lon": -0.2292735}, {"type": "node", "id": **********, "lat": 5.5423785, "lon": -0.2291916}, {"type": "node", "id": **********, "lat": 5.5422416, "lon": -0.2286889}, {"type": "node", "id": **********, "lat": 5.541761, "lon": -0.2289865}, {"type": "node", "id": **********, "lat": 5.5412607, "lon": -0.2282457}, {"type": "node", "id": **********, "lat": 5.5416952, "lon": -0.2278798}, {"type": "node", "id": **********, "lat": 5.5422523, "lon": -0.2294153}, {"type": "node", "id": **********, "lat": 5.5421677, "lon": -0.2294713}, {"type": "node", "id": **********, "lat": 5.5420147, "lon": -0.2292071}, {"type": "node", "id": **********, "lat": 5.5420854, "lon": -0.2291604}, {"type": "node", "id": **********, "lat": 5.5738643, "lon": -0.1124405, "tags": {"entrance": "main"}}, {"type": "node", "id": 10537834823, "lat": 5.6337052, "lon": -0.0983326, "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Inkoom Hospital"}}, {"type": "node", "id": 10724615217, "lat": 5.6316479, "lon": -0.1839915}, {"type": "node", "id": 10724615218, "lat": 5.6307244, "lon": -0.1834698}, {"type": "node", "id": 11001930207, "lat": 5.6509472, "lon": -0.1775024}, {"type": "node", "id": 11001930209, "lat": 5.6508631, "lon": -0.1774548}, {"type": "node", "id": 11082706568, "lat": 5.5328819, "lon": -0.2372229, "tags": {"addr:city": "Accra", "addr:housenumber": "667/1", "addr:postcode": "GA320", "addr:street": "<PERSON>", "amenity": "hospital", "healthcare": "hospital", "healthcare:speciality": "radiology;otolaryngology;cardiology", "name": "Scanport Healthcare and Medical Imaging", "operator": "Scanport Medical Diagnostics", "operator:type": "private"}}, {"type": "node", "id": 11656935633, "lat": 5.5886143, "lon": -0.1862867}, {"type": "node", "id": 11656935634, "lat": 5.5884043, "lon": -0.1870811}, {"type": "node", "id": 11656935635, "lat": 5.5881734, "lon": -0.1870244}, {"type": "node", "id": 11656935636, "lat": 5.5880949, "lon": -0.1869924}, {"type": "node", "id": 11656935637, "lat": 5.5880567, "lon": -0.1867762}, {"type": "node", "id": 11656935638, "lat": 5.5877749, "lon": -0.1867967}, {"type": "node", "id": 11656935639, "lat": 5.5861978, "lon": -0.1864087}, {"type": "node", "id": 11656935640, "lat": 5.5835117, "lon": -0.1863279}, {"type": "node", "id": 11656935641, "lat": 5.5833997, "lon": -0.1861759}, {"type": "node", "id": 11656935642, "lat": 5.5830529, "lon": -0.1853126}, {"type": "node", "id": 11656935643, "lat": 5.5841343, "lon": -0.1848502}, {"type": "node", "id": 11656935644, "lat": 5.5903073, "lon": -0.1829954}, {"type": "node", "id": 11656935645, "lat": 5.5903271, "lon": -0.1830643}, {"type": "node", "id": 11656935646, "lat": 5.5907716, "lon": -0.1832797}, {"type": "node", "id": 11820474902, "lat": 5.6088667, "lon": -0.2536887}, {"type": "node", "id": ***********, "lat": 5.5421202, "lon": -0.2293988}, {"type": "node", "id": ***********, "lat": 5.5421342, "lon": -0.2293895}, {"type": "node", "id": ***********, "lat": 5.54227, "lon": -0.2293488}, {"type": "node", "id": ***********, "lat": 5.5422519, "lon": -0.2293211}, {"type": "node", "id": ***********, "lat": 5.5422448, "lon": -0.2293257}, {"type": "node", "id": ***********, "lat": 5.5423217, "lon": -0.2292283}, {"type": "node", "id": ***********, "lat": 5.5423128, "lon": -0.2292144}, {"type": "node", "id": ***********, "lat": 5.5422626, "lon": -0.2293536}, {"type": "node", "id": 12620569905, "lat": 5.551993, "lon": -0.2644773, "tags": {"barrier": "gate"}}, {"type": "node", "id": 12715180475, "lat": 5.5765942, "lon": -0.1394296}, {"type": "node", "id": 12715180476, "lat": 5.576635, "lon": -0.1394631}, {"type": "node", "id": 12715180477, "lat": 5.5766606, "lon": -0.1394317}, {"type": "node", "id": 12715180478, "lat": 5.5766199, "lon": -0.1393982}, {"type": "node", "id": 12715180491, "lat": 5.5767035, "lon": -0.1394671}, {"type": "node", "id": 12715180492, "lat": 5.5766099, "lon": -0.1395819}, {"type": "node", "id": 12715180493, "lat": 5.5764757, "lon": -0.1394713}, {"type": "node", "id": 12715180494, "lat": 5.5765693, "lon": -0.1393566}, {"type": "node", "id": 12917692820, "lat": 5.6060797, "lon": -0.2254035, "tags": {"amenity": "hospital", "contact:facebook": "https://www.facebook.com/aimshospitalgh/", "healthcare": "hospital", "name": "AIMS Hospital"}}, {"type": "node", "id": 12917692821, "lat": 5.6060615, "lon": -0.225648, "tags": {"amenity": "hospital", "healthcare": "hospital", "healthcare:speciality": "ophthalmology", "opening_hours": "Mo-Fr 08:00-17:00; Sa 8:00-12:00", "operator": "Dr. <PERSON><PERSON><PERSON>'s Eye Hospital", "operator:type": "private", "operator:wikidata": "Q17052044", "phone": "+233 553 019311", "website": "https://www.dragarwal.com/eye-hospital/accra/"}}, {"type": "way", "id": 255511063, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Ghana Police Hospital"}}, {"type": "way", "id": 278268702, "nodes": [**********, 11001930207, 11001930209, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "University of Ghana Hospital"}}, {"type": "way", "id": 299668118, "nodes": [**********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Nyaho Clinic", "toilets:wheelchair": "yes", "wheelchair": "yes"}}, {"type": "way", "id": 310088363, "nodes": [**********, 11820474902, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "emergency": "yes", "healthcare": "hospital", "name": "Lapaz Community Hospital"}}, {"type": "way", "id": 439346942, "nodes": [**********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "building": "office", "building:levels": "2", "healthcare": "hospital", "name": "Civil Service Polyclinic"}}, {"type": "way", "id": 464683570, "nodes": [10724615218, 10724615217, **********, **********, **********, **********, 10724615218], "tags": {"addr:city": "Legon, Accra", "addr:postcode": "LG 25", "addr:street": "Jubilee Link", "alt_name": "UG Medical Centre", "amenity": "hospital", "emergency": "yes", "healthcare": "hospital", "healthcare:speciality": "general;paediatrics;ophthalmology;dialysis;dentistry;vaccination;dermatology;surgery;maternity;blood_check;community", "name": "University of Ghana Medical Center Limited", "operator": "University of Ghana", "operator:type": "government_facility-public", "phone": "+233 302 550843", "short_name": "UGMC", "website": "https://ugmedicalcentre.org/"}}, {"type": "way", "id": 543467738, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "building": "yes", "healthcare": "hospital", "name": "Grace Memorial Hospital"}}, {"type": "way", "id": 597939156, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "building": "yes", "healthcare": "hospital", "name": "SSNIT Mother and Child"}}, {"type": "way", "id": 614521670, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********, **********], "tags": {"addr:street": "Kpetenkple Street", "amenity": "hospital", "building": "yes", "healthcare": "hospital", "name": "Faith Evangelical Mission Hospital", "opening_hours": "24/7"}}, {"type": "way", "id": 635023706, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, **********], "tags": {"addr:city": "Accra", "addr:community": "Uran Clinic;<PERSON><PERSON><PERSON><PERSON><PERSON>", "addr:street": "Dzagble Avenue", "addr:suburb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amenity": "hospital", "building": "hospital", "building:levels": "2", "building:material": "cement_block", "dispensing": "yes", "emergency": "yes", "healthcare": "hospital", "name": "Al-Ayar Hospital", "opening_hours": "24/7", "operator": "private_profit", "phone": "**********", "roof:material": "metal", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "way", "id": 710756868, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********], "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Mallam Kamka Street", "addr:suburb": "Research", "amenity": "hospital", "building": "hospital", "building:levels": "3", "building:material": "cement_block", "healthcare": "hospital", "name": "<PERSON><PERSON><PERSON> and Maternity Clinic", "opening_hours": "24/7", "phone": "+233208183572", "roof:material": "concrete", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "way", "id": 785855366, "nodes": [**********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "check_date": "2025-03-10", "healthcare": "hospital", "name": "SSNIT Hospital"}}, {"type": "way", "id": 785886182, "nodes": [**********, 7346459153, 7346459154, 7346459155, 7346459156, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Valley View Clinic"}}, {"type": "way", "id": 785913649, "nodes": [7346764594, 7346764595, 7346764596, 7346764597, 7346764594], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "German Egon Clinic"}}, {"type": "way", "id": 785915055, "nodes": [7346780723, 7346780724, 7346780725, 7346780726, 7346780727, 7346780728, 7346780729, 7346780730, 7346780731, **********, 7346780723], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Family Health Hospital"}}, {"type": "way", "id": 785917727, "nodes": [7346795601, 7346795602, 7346795603, 7346795604, 7346795605, 7346795606, 7346795601], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Lekma Hospital", "wikidata": "Q59881745"}}, {"type": "way", "id": 785918690, "nodes": [7346824065, 7346824066, 7346824067, 7346824068, 7346824069, 7346824070, 7346824071, 7346824072, 7346824073, 7346824074, 7346824075, 7346824076, 7346824065], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Lister Hospital"}}, {"type": "way", "id": 786143438, "nodes": [11656935646, 11656935645, 11656935644, 7350120726, 7350120727, 7350120725, 7350120724, 7350120722, 11656935643, 11656935642, 11656935641, 11656935640, **********, **********, **********, **********, 11656935639, 7350120723, 11656935638, 11656935637, 11656935636, 11656935635, 11656935634, 11656935633, 107552545, 11656935646], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "37 Military Hospital", "wikidata": "Q16733301"}}, {"type": "way", "id": 786211189, "nodes": [7350897428, 7350897429, **********, 7350897430, 7350897431, 7350897432, 7350897433, 7350897434, 7350897435, 7350897436, 7350897437, 7350897438, 7350897428], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Adabraka Polyclinic"}}, {"type": "way", "id": 786328111, "nodes": [7352248049, 7352248050, 7352248051, 7352248052, 7352248049], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Obaatan Pa Womens Hospital"}}, {"type": "way", "id": 786331457, "nodes": [7352245782, 7352245783, 7352245784, 7352314985, 7352314986, 7352314987, 7352245782], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Cocoa Clinic"}}, {"type": "way", "id": 786508246, "nodes": [7354126353, 7354126354, 7354126355, 7354126356, 7354126353], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Barnor Hospital", "source": "locals knowledge"}}, {"type": "way", "id": 786529847, "nodes": [**********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Anthon Memorial Hospital"}}, {"type": "way", "id": 786544961, "nodes": [**********, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Accra Medical Centre", "operator": "Accra Medical Centre"}}, {"type": "way", "id": 786943766, "nodes": [**********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "International Homeopatic Clinic"}}, {"type": "way", "id": 787118973, "nodes": [**********, **********, **********, 12620569905, **********, **********], "tags": {"addr:city": "<PERSON><PERSON><PERSON>", "amenity": "hospital", "healthcare": "hospital", "healthcare:speciality": "paediatrics;midwifery", "name": "Nyameadom Maternity Home", "operator": "Midwife <PERSON><PERSON>", "operator:type": "private"}}, {"type": "way", "id": 787121870, "nodes": [**********, **********, **********, **********, **********], "tags": {"addr:street": "Allotey Bulley Road", "amenity": "hospital", "emergency": "no", "healthcare": "hospital", "healthcare:speciality": "general", "name": "Dr. <PERSON><PERSON><PERSON>'s Central Dansoman Clinic", "operator": "Private"}}, {"type": "way", "id": 787139418, "nodes": [**********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "La General Hospital", "name:it": "La General", "opening_hours": "24/7", "wikidata": "Q59882197"}}, {"type": "way", "id": 787174959, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Euracare", "phone": "**********"}}, {"type": "way", "id": 787397299, "nodes": [**********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "email": "<EMAIL>", "healthcare": "hospital", "name": "International Medical & Dental Clinic", "phone": "**********", "website": "http://www.imd-clinic.com"}}, {"type": "way", "id": 787456916, "nodes": [**********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "emergency": "yes", "healthcare": "hospital", "healthcare:speciality": "general;internal;paediatrics;cardiology", "name": "Yeboah Hospital"}}, {"type": "way", "id": 787491535, "nodes": [**********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Emmanuel Community Hospital", "opening_hours": "24/7"}}, {"type": "way", "id": 788120625, "nodes": [**********, **********, **********, **********, **********], "tags": {"addr:city": "Accra", "addr:country": "GH", "addr:postcode": "3649", "addr:street": "Tetteh Kwei Street", "amenity": "hospital", "description": "Best Eye care/clinic in Ghana", "email": "<EMAIL>", "emergency": "no", "healthcare": "hospital", "healthcare:speciality": "ophthalmology;optometry", "name": "Van J <PERSON> Care", "operator": "Rev Dr <PERSON>", "operator:type": "private", "phone": "+233 208130301", "start_date": "May 2003", "website": "https://www.vanjeyecare.org/", "wheelchair": "yes"}}, {"type": "way", "id": *********, "nodes": [**********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "Armah Memorial"}}, {"type": "way", "id": *********, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "The Bank Hospital Anex"}}, {"type": "way", "id": *********, "nodes": [**********, **********, **********, **********, **********], "tags": {"amenity": "hospital", "healthcare": "hospital", "name": "The Bank Hospital", "wikidata": "Q92826507"}}, {"type": "way", "id": *********, "nodes": [**********, **********, ***********, ***********, ***********, ***********, **********, ***********, ***********, **********, **********], "tags": {"addr:city": "Accra", "amenity": "hospital", "healthcare": "hospital", "healthcare:speciality": "dialysis", "name": "Dialysis Center", "operator": "<PERSON><PERSON> Teaching Hospital", "operator:type": "public"}}, {"type": "way", "id": *********, "nodes": [**********, **********, **********, **********, **********], "tags": {"addr:city": "Accra", "addr:street": "Slater Avenue", "amenity": "hospital", "emergency": "yes", "healthcare": "hospital", "healthcare:speciality": "urology;dialysis;nephrology", "name": "Urology & Nephrology Centre of Excellence", "operator": "<PERSON><PERSON> Teaching Hospital", "operator:type": "public", "website": "https://www.vamed.com/"}}, {"type": "way", "id": *********, "nodes": [**********, **********, ***********, ***********, **********, **********, **********], "tags": {"addr:city": "Accra", "addr:street": "Slater Avenue", "amenity": "hospital", "healthcare": "hospital", "healthcare:speciality": "dialysis;urology", "name": "Vamed Engineering Site office", "operator": "Vamed Engineering", "operator:type": "private"}}, {"type": "way", "id": **********, "nodes": [12715180494, 12715180493, 12715180492, 12715180491, 12715180477, 12715180476, 12715180475, 12715180478, 12715180494], "tags": {"amenity": "hospital", "building": "yes", "healthcare": "hospital", "name": "Rhema Rapha Medical Center"}}]}