{"version": 0.6, "generator": "Overpass API 0.7.62.7 375dc00a", "osm3s": {"timestamp_osm_base": "2025-06-30T13:11:16Z", "copyright": "The data included in this document is from www.openstreetmap.org. The data is made available under ODbL."}, "elements": [{"type": "node", "id": 6110612513, "lat": 5.5875936, "lon": -0.2177458}, {"type": "node", "id": 6110612514, "lat": 5.5876368, "lon": -0.2177561}, {"type": "node", "id": 6110612515, "lat": 5.5876265, "lon": -0.2178002}, {"type": "node", "id": 6110612516, "lat": 5.5875832, "lon": -0.21779}, {"type": "node", "id": 6255659740, "lat": 5.5585916, "lon": -0.1925249, "tags": {"frequency": "50", "generator:method": "combustion", "generator:source": "diesel", "power": "generator"}}, {"type": "node", "id": 6300118420, "lat": 5.6561166, "lon": -0.1875022}, {"type": "node", "id": 6300118421, "lat": 5.6560755, "lon": -0.1875022}, {"type": "node", "id": 6300118422, "lat": 5.6560832, "lon": -0.1874533}, {"type": "node", "id": 6300118423, "lat": 5.6561191, "lon": -0.1874533}, {"type": "node", "id": 7013543054, "lat": 5.6103696, "lon": -0.2375923}, {"type": "node", "id": 7013543055, "lat": 5.61037, "lon": -0.2375672}, {"type": "node", "id": 7013543056, "lat": 5.6103833, "lon": -0.2375675}, {"type": "node", "id": 7013543057, "lat": 5.6103829, "lon": -0.2375926}, {"type": "node", "id": 11226871362, "lat": 5.6354019, "lon": -0.1864353}, {"type": "node", "id": 11226871363, "lat": 5.6353738, "lon": -0.1864849}, {"type": "node", "id": 11226871364, "lat": 5.635306, "lon": -0.1864499}, {"type": "node", "id": 11226871365, "lat": 5.6353331, "lon": -0.1864031}, {"type": "node", "id": 11915071926, "lat": 5.6337893, "lon": -0.1812181}, {"type": "node", "id": 11915071927, "lat": 5.6337566, "lon": -0.1812071}, {"type": "node", "id": 11915071928, "lat": 5.6337943, "lon": -0.1810855}, {"type": "node", "id": 11915071929, "lat": 5.633828, "lon": -0.1810975}, {"type": "node", "id": 11915075279, "lat": 5.6339674, "lon": -0.181706}, {"type": "node", "id": 11915075280, "lat": 5.6339346, "lon": -0.1816951}, {"type": "node", "id": 11915075281, "lat": 5.6339723, "lon": -0.1815735}, {"type": "node", "id": 11915075282, "lat": 5.6340061, "lon": -0.1815854}, {"type": "node", "id": 11915075283, "lat": 5.6339247, "lon": -0.1816926}, {"type": "node", "id": 11915075284, "lat": 5.633892, "lon": -0.1816816}, {"type": "node", "id": 11915075285, "lat": 5.6339297, "lon": -0.18156}, {"type": "node", "id": 11915075286, "lat": 5.6339634, "lon": -0.181572}, {"type": "node", "id": 11915075287, "lat": 5.6338801, "lon": -0.1816806}, {"type": "node", "id": 11915075288, "lat": 5.6338473, "lon": -0.1816697}, {"type": "node", "id": 11915075289, "lat": 5.633885, "lon": -0.181548}, {"type": "node", "id": 11915075290, "lat": 5.6339188, "lon": -0.18156}, {"type": "node", "id": 11915075291, "lat": 5.6339207, "lon": -0.1815553}, {"type": "node", "id": 11915075292, "lat": 5.633888, "lon": -0.1815443}, {"type": "node", "id": 11915075293, "lat": 5.6339257, "lon": -0.1814227}, {"type": "node", "id": 11915075294, "lat": 5.6339594, "lon": -0.1814347}, {"type": "node", "id": 11915075295, "lat": 5.6339654, "lon": -0.1815672}, {"type": "node", "id": 11915075296, "lat": 5.6339327, "lon": -0.1815563}, {"type": "node", "id": 11915075297, "lat": 5.6339703, "lon": -0.1814347}, {"type": "node", "id": 11915075298, "lat": 5.6340041, "lon": -0.1814466}, {"type": "node", "id": 11915075299, "lat": 5.634008, "lon": -0.1815807}, {"type": "node", "id": 11915075300, "lat": 5.6339753, "lon": -0.1815697}, {"type": "node", "id": 11915075301, "lat": 5.634013, "lon": -0.1814481}, {"type": "node", "id": 11915075302, "lat": 5.6340467, "lon": -0.1814601}, {"type": "node", "id": 11915075303, "lat": 5.6339604, "lon": -0.1814257}, {"type": "node", "id": 11915075304, "lat": 5.6339277, "lon": -0.1814147}, {"type": "node", "id": 11915075305, "lat": 5.6339654, "lon": -0.1812931}, {"type": "node", "id": 11915075306, "lat": 5.6339991, "lon": -0.1813051}, {"type": "node", "id": 11915075307, "lat": 5.6340051, "lon": -0.1814376}, {"type": "node", "id": 11915075308, "lat": 5.6339723, "lon": -0.1814267}, {"type": "node", "id": 11915075309, "lat": 5.63401, "lon": -0.1813051}, {"type": "node", "id": 11915075310, "lat": 5.6340438, "lon": -0.181317}, {"type": "node", "id": 11915075311, "lat": 5.6340477, "lon": -0.1814511}, {"type": "node", "id": 11915075312, "lat": 5.634015, "lon": -0.1814401}, {"type": "node", "id": 11915075313, "lat": 5.6340527, "lon": -0.1813185}, {"type": "node", "id": 11915075314, "lat": 5.6340864, "lon": -0.1813305}, {"type": "node", "id": 11915075315, "lat": 5.6340031, "lon": -0.1812931}, {"type": "node", "id": 11915075316, "lat": 5.6339703, "lon": -0.1812822}, {"type": "node", "id": 11915075317, "lat": 5.634008, "lon": -0.1811605}, {"type": "node", "id": 11915075318, "lat": 5.6340418, "lon": -0.1811725}, {"type": "node", "id": 11915075319, "lat": 5.6340477, "lon": -0.1813051}, {"type": "node", "id": 11915075320, "lat": 5.634015, "lon": -0.1812941}, {"type": "node", "id": 11915075321, "lat": 5.6340527, "lon": -0.1811725}, {"type": "node", "id": 11915075322, "lat": 5.6340864, "lon": -0.1811845}, {"type": "node", "id": 11915075323, "lat": 5.6340904, "lon": -0.1813185}, {"type": "node", "id": 11915075324, "lat": 5.6340576, "lon": -0.1813076}, {"type": "node", "id": 11915075325, "lat": 5.6340953, "lon": -0.181186}, {"type": "node", "id": 11915075326, "lat": 5.6341291, "lon": -0.1811979}, {"type": "node", "id": 11915075327, "lat": 5.6338741, "lon": -0.1812503}, {"type": "node", "id": 11915075328, "lat": 5.6338414, "lon": -0.1812393}, {"type": "node", "id": 11915075329, "lat": 5.6338791, "lon": -0.1811177}, {"type": "node", "id": 11915075330, "lat": 5.6339128, "lon": -0.1811296}, {"type": "node", "id": 11915075331, "lat": 5.6339188, "lon": -0.1812622}, {"type": "node", "id": 11915075332, "lat": 5.633886, "lon": -0.1812513}, {"type": "node", "id": 11915075333, "lat": 5.6339237, "lon": -0.1811296}, {"type": "node", "id": 11915075334, "lat": 5.6339575, "lon": -0.1811416}, {"type": "node", "id": 11915075335, "lat": 5.6339614, "lon": -0.1812757}, {"type": "node", "id": 11915075336, "lat": 5.6339287, "lon": -0.1812647}, {"type": "node", "id": 11915075337, "lat": 5.6339664, "lon": -0.1811431}, {"type": "node", "id": 11915075338, "lat": 5.6340001, "lon": -0.1811551}, {"type": "node", "id": 11915075339, "lat": 5.6338344, "lon": -0.1813798}, {"type": "node", "id": 11915075340, "lat": 5.6338017, "lon": -0.1813689}, {"type": "node", "id": 11915075341, "lat": 5.6338394, "lon": -0.1812473}, {"type": "node", "id": 11915075342, "lat": 5.6338731, "lon": -0.1812592}, {"type": "node", "id": 11915075343, "lat": 5.6338791, "lon": -0.1813918}, {"type": "node", "id": 11915075344, "lat": 5.6338463, "lon": -0.1813808}, {"type": "node", "id": 11915075345, "lat": 5.633884, "lon": -0.1812592}, {"type": "node", "id": 11915075346, "lat": 5.6339178, "lon": -0.1812712}, {"type": "node", "id": 11915075347, "lat": 5.6339217, "lon": -0.1814053}, {"type": "node", "id": 11915075348, "lat": 5.633889, "lon": -0.1813943}, {"type": "node", "id": 11915075349, "lat": 5.6339267, "lon": -0.1812727}, {"type": "node", "id": 11915075350, "lat": 5.6339604, "lon": -0.1812846}, {"type": "node", "id": 11915075351, "lat": 5.6337898, "lon": -0.1815064}, {"type": "node", "id": 11915075352, "lat": 5.6337571, "lon": -0.1814955}, {"type": "node", "id": 11915075353, "lat": 5.6337948, "lon": -0.1813739}, {"type": "node", "id": 11915075354, "lat": 5.6338285, "lon": -0.1813858}, {"type": "node", "id": 11915075355, "lat": 5.6338344, "lon": -0.1815184}, {"type": "node", "id": 11915075356, "lat": 5.6338017, "lon": -0.1815074}, {"type": "node", "id": 11915075357, "lat": 5.6338394, "lon": -0.1813858}, {"type": "node", "id": 11915075358, "lat": 5.6338731, "lon": -0.1813978}, {"type": "node", "id": 11915075359, "lat": 5.6338771, "lon": -0.1815318}, {"type": "node", "id": 11915075360, "lat": 5.6338444, "lon": -0.1815209}, {"type": "node", "id": 11915075361, "lat": 5.6338821, "lon": -0.1813993}, {"type": "node", "id": 11915075362, "lat": 5.6339158, "lon": -0.1814112}, {"type": "node", "id": 11915075363, "lat": 5.6337472, "lon": -0.181636}, {"type": "node", "id": 11915075364, "lat": 5.6337144, "lon": -0.181625}, {"type": "node", "id": 11915075365, "lat": 5.6337521, "lon": -0.1815034}, {"type": "node", "id": 11915075366, "lat": 5.6337858, "lon": -0.1815154}, {"type": "node", "id": 11915075367, "lat": 5.6337918, "lon": -0.181648}, {"type": "node", "id": 11915075368, "lat": 5.6337591, "lon": -0.181637}, {"type": "node", "id": 11915087369, "lat": 5.6337968, "lon": -0.1815154}, {"type": "node", "id": 11915087370, "lat": 5.6338305, "lon": -0.1815274}, {"type": "node", "id": 11915087371, "lat": 5.6338344, "lon": -0.1816614}, {"type": "node", "id": 11915087372, "lat": 5.6338017, "lon": -0.1816505}, {"type": "node", "id": 11915087373, "lat": 5.6338394, "lon": -0.1815289}, {"type": "node", "id": 11915087374, "lat": 5.6338731, "lon": -0.1815408}, {"type": "node", "id": 11915087375, "lat": 5.6336202, "lon": -0.1815961}, {"type": "node", "id": 11915087376, "lat": 5.6335874, "lon": -0.1815852}, {"type": "node", "id": 11915087377, "lat": 5.6336251, "lon": -0.1814636}, {"type": "node", "id": 11915087378, "lat": 5.6336589, "lon": -0.1814755}, {"type": "node", "id": 11915087379, "lat": 5.6336648, "lon": -0.1816081}, {"type": "node", "id": 11915087380, "lat": 5.6336321, "lon": -0.1815971}, {"type": "node", "id": 11915087381, "lat": 5.6336698, "lon": -0.1814755}, {"type": "node", "id": 11915087382, "lat": 5.6337035, "lon": -0.1814875}, {"type": "node", "id": 11915087383, "lat": 5.6337075, "lon": -0.1816216}, {"type": "node", "id": 11915087384, "lat": 5.6336747, "lon": -0.1816106}, {"type": "node", "id": 11915087385, "lat": 5.6337124, "lon": -0.181489}, {"type": "node", "id": 11915087386, "lat": 5.6337462, "lon": -0.1815009}, {"type": "node", "id": 11915087387, "lat": 5.6336609, "lon": -0.1814656}, {"type": "node", "id": 11915087388, "lat": 5.6336281, "lon": -0.1814546}, {"type": "node", "id": 11915087389, "lat": 5.6336658, "lon": -0.181333}, {"type": "node", "id": 11915087390, "lat": 5.6336995, "lon": -0.1813449}, {"type": "node", "id": 11915087391, "lat": 5.6337055, "lon": -0.1814775}, {"type": "node", "id": 11915087392, "lat": 5.6336728, "lon": -0.1814666}, {"type": "node", "id": 11915087393, "lat": 5.6337105, "lon": -0.1813449}, {"type": "node", "id": 11915087394, "lat": 5.6337442, "lon": -0.1813569}, {"type": "node", "id": 11915087395, "lat": 5.6337481, "lon": -0.181491}, {"type": "node", "id": 11915087396, "lat": 5.6337154, "lon": -0.18148}, {"type": "node", "id": 11915087397, "lat": 5.6337531, "lon": -0.1813584}, {"type": "node", "id": 11915087398, "lat": 5.6337868, "lon": -0.1813704}, {"type": "node", "id": 11915087399, "lat": 5.6337055, "lon": -0.181336}, {"type": "node", "id": 11915087400, "lat": 5.6336728, "lon": -0.181325}, {"type": "node", "id": 11915087401, "lat": 5.6337105, "lon": -0.1812034}, {"type": "node", "id": 11915087402, "lat": 5.6337442, "lon": -0.1812154}, {"type": "node", "id": 11915087403, "lat": 5.6337501, "lon": -0.1813479}, {"type": "node", "id": 11915087404, "lat": 5.6337174, "lon": -0.181337}, {"type": "node", "id": 11915087405, "lat": 5.6337551, "lon": -0.1812154}, {"type": "node", "id": 11915087406, "lat": 5.6337888, "lon": -0.1812273}, {"type": "node", "id": 11915087407, "lat": 5.6337928, "lon": -0.1813614}, {"type": "node", "id": 11915087408, "lat": 5.63376, "lon": -0.1813504}, {"type": "node", "id": 11915087409, "lat": 5.6337977, "lon": -0.1812288}, {"type": "node", "id": 11915087410, "lat": 5.6338315, "lon": -0.1812408}, {"type": "node", "id": 11915087411, "lat": 5.6337472, "lon": -0.1812064}, {"type": "node", "id": 11915087412, "lat": 5.6337144, "lon": -0.1811954}, {"type": "node", "id": 11915087413, "lat": 5.6337521, "lon": -0.1810738}, {"type": "node", "id": 11915087414, "lat": 5.6337858, "lon": -0.1810858}, {"type": "node", "id": 11915087419, "lat": 5.6338344, "lon": -0.1812318}, {"type": "node", "id": 11915087420, "lat": 5.6338017, "lon": -0.1812208}, {"type": "node", "id": 11915087421, "lat": 5.6338394, "lon": -0.1810992}, {"type": "node", "id": 11915087422, "lat": 5.6338731, "lon": -0.1811112}, {"type": "node", "id": 11915087423, "lat": 5.6340557, "lon": -0.1811366}, {"type": "node", "id": 11915087424, "lat": 5.6340229, "lon": -0.1811257}, {"type": "node", "id": 11915087425, "lat": 5.6340606, "lon": -0.181004}, {"type": "node", "id": 11915087426, "lat": 5.6340943, "lon": -0.181016}, {"type": "node", "id": 11915087427, "lat": 5.6341003, "lon": -0.1811486}, {"type": "node", "id": 11915087428, "lat": 5.6340676, "lon": -0.1811376}, {"type": "node", "id": 11915087429, "lat": 5.6341053, "lon": -0.181016}, {"type": "node", "id": 11915087430, "lat": 5.634139, "lon": -0.181028}, {"type": "node", "id": 11915087431, "lat": 5.6341429, "lon": -0.181162}, {"type": "node", "id": 11915087432, "lat": 5.6341102, "lon": -0.1811511}, {"type": "node", "id": 11915087433, "lat": 5.6341479, "lon": -0.1810295}, {"type": "node", "id": 11915087434, "lat": 5.6341816, "lon": -0.1810414}, {"type": "node", "id": 11915087435, "lat": 5.6339277, "lon": -0.1810928}, {"type": "node", "id": 11915087436, "lat": 5.633895, "lon": -0.1810818}, {"type": "node", "id": 11915087437, "lat": 5.6339327, "lon": -0.1809602}, {"type": "node", "id": 11915087438, "lat": 5.6339664, "lon": -0.1809722}, {"type": "node", "id": 11915087439, "lat": 5.6339723, "lon": -0.1811047}, {"type": "node", "id": 11915087440, "lat": 5.6339396, "lon": -0.1810938}, {"type": "node", "id": 11915087441, "lat": 5.6339773, "lon": -0.1809722}, {"type": "node", "id": 11915087442, "lat": 5.634011, "lon": -0.1809841}, {"type": "node", "id": 11915087443, "lat": 5.634015, "lon": -0.1811182}, {"type": "node", "id": 11915087444, "lat": 5.6339822, "lon": -0.1811072}, {"type": "node", "id": 11915087445, "lat": 5.6340199, "lon": -0.1809856}, {"type": "node", "id": 11915087446, "lat": 5.6340537, "lon": -0.1809976}, {"type": "node", "id": 11915087447, "lat": 5.6337997, "lon": -0.1810509}, {"type": "node", "id": 11915087448, "lat": 5.633767, "lon": -0.1810399}, {"type": "node", "id": 11915087449, "lat": 5.6338047, "lon": -0.1809183}, {"type": "node", "id": 11915087450, "lat": 5.6338384, "lon": -0.1809303}, {"type": "node", "id": 11915087451, "lat": 5.6338444, "lon": -0.1810629}, {"type": "node", "id": 11915087452, "lat": 5.6338116, "lon": -0.1810519}, {"type": "node", "id": 11915087453, "lat": 5.6338493, "lon": -0.1809303}, {"type": "node", "id": 11915087454, "lat": 5.6338831, "lon": -0.1809422}, {"type": "node", "id": 11915087455, "lat": 5.633887, "lon": -0.1810763}, {"type": "node", "id": 11915087456, "lat": 5.6338543, "lon": -0.1810654}, {"type": "node", "id": 11915087457, "lat": 5.633892, "lon": -0.1809437}, {"type": "node", "id": 11915087458, "lat": 5.6339257, "lon": -0.1809557}, {"type": "node", "id": 11915087459, "lat": 5.6340993, "lon": -0.181004}, {"type": "node", "id": 11915087460, "lat": 5.6340666, "lon": -0.1809931}, {"type": "node", "id": 11915087461, "lat": 5.6341043, "lon": -0.1808715}, {"type": "node", "id": 11915087462, "lat": 5.634138, "lon": -0.1808834}, {"type": "node", "id": 11915087463, "lat": 5.6341439, "lon": -0.181016}, {"type": "node", "id": 11915087464, "lat": 5.6341112, "lon": -0.181005}, {"type": "node", "id": 11915087465, "lat": 5.6341489, "lon": -0.1808834}, {"type": "node", "id": 11915087466, "lat": 5.6341826, "lon": -0.1808954}, {"type": "node", "id": 11915087467, "lat": 5.6341866, "lon": -0.1810295}, {"type": "node", "id": 11915087468, "lat": 5.6341539, "lon": -0.1810185}, {"type": "node", "id": 11915087469, "lat": 5.6341916, "lon": -0.1808969}, {"type": "node", "id": 11915087470, "lat": 5.6342253, "lon": -0.1809089}, {"type": "node", "id": 11915087471, "lat": 5.6339723, "lon": -0.1809632}, {"type": "node", "id": 11915087472, "lat": 5.6339396, "lon": -0.1809522}, {"type": "node", "id": 11915087473, "lat": 5.6339773, "lon": -0.1808306}, {"type": "node", "id": 11915087474, "lat": 5.634011, "lon": -0.1808426}, {"type": "node", "id": 11915087475, "lat": 5.634017, "lon": -0.1809751}, {"type": "node", "id": 11915087476, "lat": 5.6339842, "lon": -0.1809642}, {"type": "node", "id": 11915087477, "lat": 5.6340219, "lon": -0.1808426}, {"type": "node", "id": 11915087478, "lat": 5.6340557, "lon": -0.1808545}, {"type": "node", "id": 11915087479, "lat": 5.6340596, "lon": -0.1809886}, {"type": "node", "id": 11915087480, "lat": 5.6340269, "lon": -0.1809776}, {"type": "node", "id": 11915087481, "lat": 5.6340646, "lon": -0.180856}, {"type": "node", "id": 11915087482, "lat": 5.6340983, "lon": -0.180868}, {"type": "node", "id": 11915087483, "lat": 5.6338434, "lon": -0.1809233}, {"type": "node", "id": 11915087484, "lat": 5.6338106, "lon": -0.1809123}, {"type": "node", "id": 11915087485, "lat": 5.6338483, "lon": -0.1807907}, {"type": "node", "id": 11915087486, "lat": 5.6338821, "lon": -0.1808027}, {"type": "node", "id": 11915087487, "lat": 5.633888, "lon": -0.1809353}, {"type": "node", "id": 11915087488, "lat": 5.6338553, "lon": -0.1809243}, {"type": "node", "id": 11915087489, "lat": 5.633893, "lon": -0.1808027}, {"type": "node", "id": 11915087490, "lat": 5.6339267, "lon": -0.1808147}, {"type": "node", "id": 11915087491, "lat": 5.6339307, "lon": -0.1809487}, {"type": "node", "id": 11915087492, "lat": 5.6338979, "lon": -0.1809378}, {"type": "node", "id": 11915087493, "lat": 5.6339356, "lon": -0.1808162}, {"type": "node", "id": 11915087494, "lat": 5.6339694, "lon": -0.1808281}, {"type": "node", "id": 11915087495, "lat": 5.6337065, "lon": -0.1808735}, {"type": "node", "id": 11915087496, "lat": 5.6336737, "lon": -0.1808625}, {"type": "node", "id": 11915087497, "lat": 5.6337114, "lon": -0.1807409}, {"type": "node", "id": 11915087498, "lat": 5.6337452, "lon": -0.1807529}, {"type": "node", "id": 11915087499, "lat": 5.6337511, "lon": -0.1808854}, {"type": "node", "id": 11915087500, "lat": 5.6337184, "lon": -0.1808745}, {"type": "node", "id": 11915087501, "lat": 5.6337561, "lon": -0.1807529}, {"type": "node", "id": 11915087502, "lat": 5.6337898, "lon": -0.1807648}, {"type": "node", "id": 11915087503, "lat": 5.6337938, "lon": -0.1808989}, {"type": "node", "id": 11915087504, "lat": 5.633761, "lon": -0.1808879}, {"type": "node", "id": 11915087505, "lat": 5.6337987, "lon": -0.1807663}, {"type": "node", "id": 11915087506, "lat": 5.6338325, "lon": -0.1807783}, {"type": "node", "id": 11915087507, "lat": 5.6336648, "lon": -0.1810011}, {"type": "node", "id": 11915087508, "lat": 5.6336321, "lon": -0.1809901}, {"type": "node", "id": 11915087509, "lat": 5.6336698, "lon": -0.1808685}, {"type": "node", "id": 11915087510, "lat": 5.6337035, "lon": -0.1808804}, {"type": "node", "id": 11915087511, "lat": 5.6337095, "lon": -0.181013}, {"type": "node", "id": 11915087512, "lat": 5.6336767, "lon": -0.1810021}, {"type": "node", "id": 11915087513, "lat": 5.6337144, "lon": -0.1808804}, {"type": "node", "id": 11915087514, "lat": 5.6337481, "lon": -0.1808924}, {"type": "node", "id": 11915087515, "lat": 5.6337521, "lon": -0.1810265}, {"type": "node", "id": 11915087516, "lat": 5.6337194, "lon": -0.1810155}, {"type": "node", "id": 11915087517, "lat": 5.6337571, "lon": -0.1808939}, {"type": "node", "id": 11915087518, "lat": 5.6337908, "lon": -0.1809059}, {"type": "node", "id": 11915087519, "lat": 5.6335319, "lon": -0.1809562}, {"type": "node", "id": 11915087520, "lat": 5.6334992, "lon": -0.1809452}, {"type": "node", "id": 11915087521, "lat": 5.6335369, "lon": -0.1808236}, {"type": "node", "id": 11915087522, "lat": 5.6335706, "lon": -0.1808356}, {"type": "node", "id": 11915087523, "lat": 5.6335765, "lon": -0.1809682}, {"type": "node", "id": 11915087524, "lat": 5.6335438, "lon": -0.1809572}, {"type": "node", "id": 11915087525, "lat": 5.6335815, "lon": -0.1808356}, {"type": "node", "id": 11915087526, "lat": 5.6336152, "lon": -0.1808476}, {"type": "node", "id": 11915087527, "lat": 5.6336192, "lon": -0.1809816}, {"type": "node", "id": 11915087528, "lat": 5.6335865, "lon": -0.1809707}, {"type": "node", "id": 11915087529, "lat": 5.6336241, "lon": -0.180849}, {"type": "node", "id": 11915087530, "lat": 5.6336579, "lon": -0.180861}, {"type": "node", "id": 11915087531, "lat": 5.6335339, "lon": -0.1811007}, {"type": "node", "id": 11915087532, "lat": 5.6335011, "lon": -0.1810898}, {"type": "node", "id": 11915087533, "lat": 5.6335388, "lon": -0.1809682}, {"type": "node", "id": 11915087534, "lat": 5.6335726, "lon": -0.1809801}, {"type": "node", "id": 11915087535, "lat": 5.6335785, "lon": -0.1811127}, {"type": "node", "id": 11915087536, "lat": 5.6335458, "lon": -0.1811017}, {"type": "node", "id": 11915087537, "lat": 5.6335835, "lon": -0.1809801}, {"type": "node", "id": 11915087538, "lat": 5.6336172, "lon": -0.1809921}, {"type": "node", "id": 11915087539, "lat": 5.6336212, "lon": -0.1811262}, {"type": "node", "id": 11915087540, "lat": 5.6335884, "lon": -0.1811152}, {"type": "node", "id": 11915087541, "lat": 5.6336261, "lon": -0.1809936}, {"type": "node", "id": 11915087542, "lat": 5.6336599, "lon": -0.1810055}, {"type": "node", "id": 11915087543, "lat": 5.6334049, "lon": -0.1810609}, {"type": "node", "id": 11915087544, "lat": 5.6333722, "lon": -0.1810499}, {"type": "node", "id": 11915087545, "lat": 5.6334099, "lon": -0.1809283}, {"type": "node", "id": 11915087546, "lat": 5.6334436, "lon": -0.1809403}, {"type": "node", "id": 11915087547, "lat": 5.6334496, "lon": -0.1810728}, {"type": "node", "id": 11915087548, "lat": 5.6334168, "lon": -0.1810619}, {"type": "node", "id": 11915087549, "lat": 5.6334545, "lon": -0.1809403}, {"type": "node", "id": 11915087550, "lat": 5.6334882, "lon": -0.1809522}, {"type": "node", "id": 11915087551, "lat": 5.6334922, "lon": -0.1810863}, {"type": "node", "id": 11915087552, "lat": 5.6334595, "lon": -0.1810753}, {"type": "node", "id": 11915087553, "lat": 5.6334972, "lon": -0.1809537}, {"type": "node", "id": 11915087554, "lat": 5.6335309, "lon": -0.1809657}, {"type": "node", "id": 11915087555, "lat": 5.6334882, "lon": -0.180942}, {"type": "node", "id": 11915087556, "lat": 5.6334555, "lon": -0.180931}, {"type": "node", "id": 11915087557, "lat": 5.6334932, "lon": -0.1808094}, {"type": "node", "id": 11915087558, "lat": 5.6335269, "lon": -0.1808214}, {"type": "node", "id": 11915087559, "lat": 5.6334426, "lon": -0.1809251}, {"type": "node", "id": 11915087560, "lat": 5.6334099, "lon": -0.1809141}, {"type": "node", "id": 11915087561, "lat": 5.6334476, "lon": -0.1807925}, {"type": "node", "id": 11915087562, "lat": 5.6334813, "lon": -0.1808044}, {"type": "node", "id": 11915087563, "lat": 5.6333623, "lon": -0.1820444}, {"type": "node", "id": 11915087564, "lat": 5.6333216, "lon": -0.1820325}, {"type": "node", "id": 11915087565, "lat": 5.6333484, "lon": -0.1819418}, {"type": "node", "id": 11915087566, "lat": 5.633391, "lon": -0.1819527}, {"type": "node", "id": 11915087567, "lat": 5.633396, "lon": -0.1819428}, {"type": "node", "id": 11915087568, "lat": 5.6333553, "lon": -0.1819308}, {"type": "node", "id": 11915087569, "lat": 5.6333821, "lon": -0.1818401}, {"type": "node", "id": 11915087570, "lat": 5.6334248, "lon": -0.1818511}, {"type": "node", "id": 11915087571, "lat": 5.6334297, "lon": -0.1818451}, {"type": "node", "id": 11915087572, "lat": 5.6333891, "lon": -0.1818331}, {"type": "node", "id": 11915087573, "lat": 5.6334158, "lon": -0.1817424}, {"type": "node", "id": 11915087574, "lat": 5.6334585, "lon": -0.1817534}, {"type": "node", "id": 11915087575, "lat": 5.6334615, "lon": -0.1817424}, {"type": "node", "id": 11915087576, "lat": 5.6334208, "lon": -0.1817305}, {"type": "node", "id": 11915087577, "lat": 5.6334476, "lon": -0.1816397}, {"type": "node", "id": 11915087578, "lat": 5.6334902, "lon": -0.1816507}, {"type": "node", "id": 11915087579, "lat": 5.6333087, "lon": -0.182024}, {"type": "node", "id": 11915087580, "lat": 5.633268, "lon": -0.182012}, {"type": "node", "id": 11915087581, "lat": 5.6332948, "lon": -0.1819213}, {"type": "node", "id": 11915087582, "lat": 5.6333375, "lon": -0.1819323}, {"type": "node", "id": 11915087583, "lat": 5.6333424, "lon": -0.1819223}, {"type": "node", "id": 11915087584, "lat": 5.6333018, "lon": -0.1819104}, {"type": "node", "id": 11915087585, "lat": 5.6333285, "lon": -0.1818197}, {"type": "node", "id": 11915087586, "lat": 5.6333712, "lon": -0.1818306}, {"type": "node", "id": 11915087587, "lat": 5.6333762, "lon": -0.1818247}, {"type": "node", "id": 11915087588, "lat": 5.6333355, "lon": -0.1818127}, {"type": "node", "id": 11915087589, "lat": 5.6333623, "lon": -0.181722}, {"type": "node", "id": 11915087590, "lat": 5.6334049, "lon": -0.1817329}, {"type": "node", "id": 11915087591, "lat": 5.6334079, "lon": -0.181722}, {"type": "node", "id": 11915087592, "lat": 5.6333672, "lon": -0.18171}, {"type": "node", "id": 11915087593, "lat": 5.633394, "lon": -0.1816193}, {"type": "node", "id": 11915087594, "lat": 5.6334367, "lon": -0.1816303}, {"type": "node", "id": 11915087595, "lat": 5.6332601, "lon": -0.1820091}, {"type": "node", "id": 11915087596, "lat": 5.6332194, "lon": -0.1819971}, {"type": "node", "id": 11915087597, "lat": 5.6332462, "lon": -0.1819064}, {"type": "node", "id": 11915087598, "lat": 5.6332889, "lon": -0.1819174}, {"type": "node", "id": 11915087599, "lat": 5.6332938, "lon": -0.1819074}, {"type": "node", "id": 11915087600, "lat": 5.6332532, "lon": -0.1818954}, {"type": "node", "id": 11915087601, "lat": 5.6332799, "lon": -0.1818047}, {"type": "node", "id": 11915087602, "lat": 5.6333226, "lon": -0.1818157}, {"type": "node", "id": 11915087603, "lat": 5.6333276, "lon": -0.1818097}, {"type": "node", "id": 11915087604, "lat": 5.6332869, "lon": -0.1817977}, {"type": "node", "id": 11915087605, "lat": 5.6333137, "lon": -0.181707}, {"type": "node", "id": 11915087606, "lat": 5.6333563, "lon": -0.181718}, {"type": "node", "id": 11915087607, "lat": 5.6333593, "lon": -0.181707}, {"type": "node", "id": 11915087608, "lat": 5.6333186, "lon": -0.1816951}, {"type": "node", "id": 11915087609, "lat": 5.6333454, "lon": -0.1816044}, {"type": "node", "id": 11915087610, "lat": 5.6333881, "lon": -0.1816153}, {"type": "node", "id": 11915087611, "lat": 5.6332045, "lon": -0.1819961}, {"type": "node", "id": 11915087612, "lat": 5.6331639, "lon": -0.1819841}, {"type": "node", "id": 11915087613, "lat": 5.6331907, "lon": -0.1818934}, {"type": "node", "id": 11915087614, "lat": 5.6332333, "lon": -0.1819044}, {"type": "node", "id": 11915087615, "lat": 5.6332383, "lon": -0.1818944}, {"type": "node", "id": 11915087616, "lat": 5.6331976, "lon": -0.1818825}, {"type": "node", "id": 11915087617, "lat": 5.6332244, "lon": -0.1817918}, {"type": "node", "id": 11915087618, "lat": 5.633267, "lon": -0.1818027}, {"type": "node", "id": 11915087619, "lat": 5.633272, "lon": -0.1817967}, {"type": "node", "id": 11915087620, "lat": 5.6332313, "lon": -0.1817848}, {"type": "node", "id": 11915087621, "lat": 5.6332581, "lon": -0.1816941}, {"type": "node", "id": 11915087622, "lat": 5.6333008, "lon": -0.181705}, {"type": "node", "id": 11915087623, "lat": 5.6333037, "lon": -0.1816941}, {"type": "node", "id": 11915087624, "lat": 5.6332631, "lon": -0.1816821}, {"type": "node", "id": 11915087625, "lat": 5.6332899, "lon": -0.1815914}, {"type": "node", "id": 11915087626, "lat": 5.6333325, "lon": -0.1816024}, {"type": "node", "id": 11915087627, "lat": 5.633151, "lon": -0.1819722}, {"type": "node", "id": 11915087628, "lat": 5.6331103, "lon": -0.1819602}, {"type": "node", "id": 11915087629, "lat": 5.6331371, "lon": -0.1818695}, {"type": "node", "id": 11915087630, "lat": 5.6331797, "lon": -0.1818805}, {"type": "node", "id": 11915087631, "lat": 5.6331847, "lon": -0.1818705}, {"type": "node", "id": 11915087632, "lat": 5.633144, "lon": -0.1818585}, {"type": "node", "id": 11915087633, "lat": 5.6331708, "lon": -0.1817678}, {"type": "node", "id": 11915087634, "lat": 5.6332135, "lon": -0.1817788}, {"type": "node", "id": 11915087635, "lat": 5.6332184, "lon": -0.1817728}, {"type": "node", "id": 11915087636, "lat": 5.6331778, "lon": -0.1817609}, {"type": "node", "id": 11915087637, "lat": 5.6332045, "lon": -0.1816701}, {"type": "node", "id": 11915087638, "lat": 5.6332472, "lon": -0.1816811}, {"type": "node", "id": 11915087639, "lat": 5.6332502, "lon": -0.1816701}, {"type": "node", "id": 11915087640, "lat": 5.6332095, "lon": -0.1816582}, {"type": "node", "id": 11915087641, "lat": 5.6332363, "lon": -0.1815675}, {"type": "node", "id": 11915087642, "lat": 5.6332789, "lon": -0.1815784}, {"type": "node", "id": 11915087643, "lat": 5.6331004, "lon": -0.1819552}, {"type": "node", "id": 11915087644, "lat": 5.6330597, "lon": -0.1819433}, {"type": "node", "id": 11915087645, "lat": 5.6330865, "lon": -0.1818526}, {"type": "node", "id": 11915087646, "lat": 5.6331292, "lon": -0.1818635}, {"type": "node", "id": 11915087647, "lat": 5.6331341, "lon": -0.1818536}, {"type": "node", "id": 11915087648, "lat": 5.6330934, "lon": -0.1818416}, {"type": "node", "id": 11915087649, "lat": 5.6331202, "lon": -0.1817509}, {"type": "node", "id": 11915087650, "lat": 5.6331629, "lon": -0.1817619}, {"type": "node", "id": 11915087651, "lat": 5.6331678, "lon": -0.1817559}, {"type": "node", "id": 11915087652, "lat": 5.6331272, "lon": -0.1817439}, {"type": "node", "id": 11915087653, "lat": 5.633154, "lon": -0.1816532}, {"type": "node", "id": 11915087654, "lat": 5.6331966, "lon": -0.1816642}, {"type": "node", "id": 11915087655, "lat": 5.6331996, "lon": -0.1816532}, {"type": "node", "id": 11915087656, "lat": 5.6331589, "lon": -0.1816412}, {"type": "node", "id": 11915087657, "lat": 5.6331857, "lon": -0.1815505}, {"type": "node", "id": 11915087658, "lat": 5.6332284, "lon": -0.1815615}, {"type": "node", "id": 11915087659, "lat": 5.6330508, "lon": -0.1819373}, {"type": "node", "id": 11915087660, "lat": 5.6330101, "lon": -0.1819253}, {"type": "node", "id": 11915087661, "lat": 5.6330369, "lon": -0.1818346}, {"type": "node", "id": 11915087662, "lat": 5.6330796, "lon": -0.1818456}, {"type": "node", "id": 11915087663, "lat": 5.6330845, "lon": -0.1818356}, {"type": "node", "id": 11915087664, "lat": 5.6330438, "lon": -0.1818237}, {"type": "node", "id": 11915087665, "lat": 5.6330706, "lon": -0.1817329}, {"type": "node", "id": 11915087666, "lat": 5.6331133, "lon": -0.1817439}, {"type": "node", "id": 11915087667, "lat": 5.6331182, "lon": -0.1817379}, {"type": "node", "id": 11915087668, "lat": 5.6330776, "lon": -0.181726}, {"type": "node", "id": 11915087669, "lat": 5.6331044, "lon": -0.1816353}, {"type": "node", "id": 11915087670, "lat": 5.633147, "lon": -0.1816462}, {"type": "node", "id": 11915087671, "lat": 5.63315, "lon": -0.1816353}, {"type": "node", "id": 11915087672, "lat": 5.6331093, "lon": -0.1816233}, {"type": "node", "id": 11915087673, "lat": 5.6331361, "lon": -0.1815326}, {"type": "node", "id": 11915087674, "lat": 5.6331788, "lon": -0.1815436}, {"type": "node", "id": 11915087675, "lat": 5.6329982, "lon": -0.1819183}, {"type": "node", "id": 11915087676, "lat": 5.6329575, "lon": -0.1819064}, {"type": "node", "id": 11915087677, "lat": 5.6329843, "lon": -0.1818157}, {"type": "node", "id": 11915087678, "lat": 5.633027, "lon": -0.1818266}, {"type": "node", "id": 11915087679, "lat": 5.6330319, "lon": -0.1818167}, {"type": "node", "id": 11915087680, "lat": 5.6329913, "lon": -0.1818047}, {"type": "node", "id": 11915087681, "lat": 5.6330181, "lon": -0.181714}, {"type": "node", "id": 11915087682, "lat": 5.6330607, "lon": -0.181725}, {"type": "node", "id": 11915087683, "lat": 5.6330657, "lon": -0.181719}, {"type": "node", "id": 11915087684, "lat": 5.633025, "lon": -0.181707}, {"type": "node", "id": 11915087685, "lat": 5.6330518, "lon": -0.1816163}, {"type": "node", "id": 11915087686, "lat": 5.6330944, "lon": -0.1816273}, {"type": "node", "id": 11915087687, "lat": 5.6330974, "lon": -0.1816163}, {"type": "node", "id": 11915087688, "lat": 5.6330567, "lon": -0.1816044}, {"type": "node", "id": 11915087689, "lat": 5.6330835, "lon": -0.1815137}, {"type": "node", "id": 11915087690, "lat": 5.6331262, "lon": -0.1815246}, {"type": "node", "id": 11915087691, "lat": 5.6329456, "lon": -0.1818994}, {"type": "node", "id": 11915087692, "lat": 5.632905, "lon": -0.1818874}, {"type": "node", "id": 11915087693, "lat": 5.6329318, "lon": -0.1817967}, {"type": "node", "id": 11915087694, "lat": 5.6329744, "lon": -0.1818077}, {"type": "node", "id": 11915087695, "lat": 5.6329794, "lon": -0.1817977}, {"type": "node", "id": 11915087696, "lat": 5.6329387, "lon": -0.1817858}, {"type": "node", "id": 11915087697, "lat": 5.6329655, "lon": -0.1816951}, {"type": "node", "id": 11915087698, "lat": 5.6330081, "lon": -0.181706}, {"type": "node", "id": 11915087699, "lat": 5.6330131, "lon": -0.1817001}, {"type": "node", "id": 11915087700, "lat": 5.6329724, "lon": -0.1816881}, {"type": "node", "id": 11915087701, "lat": 5.6329992, "lon": -0.1815974}, {"type": "node", "id": 11915087702, "lat": 5.6330419, "lon": -0.1816083}, {"type": "node", "id": 11915087703, "lat": 5.6330448, "lon": -0.1815974}, {"type": "node", "id": 11915087704, "lat": 5.6330042, "lon": -0.1815854}, {"type": "node", "id": 11915087705, "lat": 5.633031, "lon": -0.1814947}, {"type": "node", "id": 11915087706, "lat": 5.6330736, "lon": -0.1815057}, {"type": "node", "id": 11915087707, "lat": 5.6328921, "lon": -0.1818825}, {"type": "node", "id": 11915087708, "lat": 5.6328514, "lon": -0.1818705}, {"type": "node", "id": 11915087709, "lat": 5.6328782, "lon": -0.1817798}, {"type": "node", "id": 11915087710, "lat": 5.6329208, "lon": -0.1817908}, {"type": "node", "id": 11915087711, "lat": 5.6329258, "lon": -0.1817808}, {"type": "node", "id": 11915087712, "lat": 5.6328851, "lon": -0.1817688}, {"type": "node", "id": 11915087713, "lat": 5.6329119, "lon": -0.1816781}, {"type": "node", "id": 11915087714, "lat": 5.6329546, "lon": -0.1816891}, {"type": "node", "id": 11915087715, "lat": 5.6329595, "lon": -0.1816831}, {"type": "node", "id": 11915087716, "lat": 5.6329189, "lon": -0.1816711}, {"type": "node", "id": 11915087717, "lat": 5.6329456, "lon": -0.1815804}, {"type": "node", "id": 11915087718, "lat": 5.6329883, "lon": -0.1815914}, {"type": "node", "id": 11915087719, "lat": 5.6329913, "lon": -0.1815804}, {"type": "node", "id": 11915087720, "lat": 5.6329506, "lon": -0.1815685}, {"type": "node", "id": 11915087721, "lat": 5.6329774, "lon": -0.1814778}, {"type": "node", "id": 11915087722, "lat": 5.63302, "lon": -0.1814887}, {"type": "node", "id": 11915087723, "lat": 5.6328405, "lon": -0.1818695}, {"type": "node", "id": 11915087724, "lat": 5.6327998, "lon": -0.1818575}, {"type": "node", "id": 11915087725, "lat": 5.6328266, "lon": -0.1817668}, {"type": "node", "id": 11915087726, "lat": 5.6328693, "lon": -0.1817778}, {"type": "node", "id": 11915087727, "lat": 5.6328742, "lon": -0.1817678}, {"type": "node", "id": 11915087728, "lat": 5.6328336, "lon": -0.1817559}, {"type": "node", "id": 11915087729, "lat": 5.6328603, "lon": -0.1816652}, {"type": "node", "id": 11915087730, "lat": 5.632903, "lon": -0.1816761}, {"type": "node", "id": 11915087731, "lat": 5.6329079, "lon": -0.1816701}, {"type": "node", "id": 11915087732, "lat": 5.6328673, "lon": -0.1816582}, {"type": "node", "id": 11915087733, "lat": 5.6328941, "lon": -0.1815675}, {"type": "node", "id": 11915087734, "lat": 5.6329367, "lon": -0.1815784}, {"type": "node", "id": 11915087735, "lat": 5.6329397, "lon": -0.1815675}, {"type": "node", "id": 11915087736, "lat": 5.632899, "lon": -0.1815555}, {"type": "node", "id": 11915087737, "lat": 5.6329258, "lon": -0.1814648}, {"type": "node", "id": 11915087738, "lat": 5.6329685, "lon": -0.1814758}, {"type": "node", "id": 11915087739, "lat": 5.6327919, "lon": -0.1818526}, {"type": "node", "id": 11915087740, "lat": 5.6327512, "lon": -0.1818406}, {"type": "node", "id": 11915087741, "lat": 5.632778, "lon": -0.1817499}, {"type": "node", "id": 11915087742, "lat": 5.6328207, "lon": -0.1817609}, {"type": "node", "id": 11915087743, "lat": 5.6328256, "lon": -0.1817509}, {"type": "node", "id": 11915087744, "lat": 5.6327849, "lon": -0.1817389}, {"type": "node", "id": 11915087745, "lat": 5.6328117, "lon": -0.1816482}, {"type": "node", "id": 11915087746, "lat": 5.6328544, "lon": -0.1816592}, {"type": "node", "id": 11915087747, "lat": 5.6328593, "lon": -0.1816532}, {"type": "node", "id": 11915087748, "lat": 5.6328187, "lon": -0.1816412}, {"type": "node", "id": 11915087749, "lat": 5.6328455, "lon": -0.1815505}, {"type": "node", "id": 11915087750, "lat": 5.6328881, "lon": -0.1815615}, {"type": "node", "id": 11915087751, "lat": 5.6328911, "lon": -0.1815505}, {"type": "node", "id": 11915087752, "lat": 5.6328504, "lon": -0.1815386}, {"type": "node", "id": 11915087753, "lat": 5.6328772, "lon": -0.1814479}, {"type": "node", "id": 11915087754, "lat": 5.6329199, "lon": -0.1814588}, {"type": "node", "id": 11915087755, "lat": 5.6327423, "lon": -0.1818296}, {"type": "node", "id": 11915087756, "lat": 5.6327016, "lon": -0.1818177}, {"type": "node", "id": 11915087757, "lat": 5.6327284, "lon": -0.181727}, {"type": "node", "id": 11915087758, "lat": 5.6327711, "lon": -0.1817379}, {"type": "node", "id": 11915087759, "lat": 5.632776, "lon": -0.181728}, {"type": "node", "id": 11915087760, "lat": 5.6327353, "lon": -0.181716}, {"type": "node", "id": 11915087761, "lat": 5.6327621, "lon": -0.1816253}, {"type": "node", "id": 11915087762, "lat": 5.6328048, "lon": -0.1816363}, {"type": "node", "id": 11915087763, "lat": 5.6328097, "lon": -0.1816303}, {"type": "node", "id": 11915087764, "lat": 5.6327691, "lon": -0.1816183}, {"type": "node", "id": 11915087765, "lat": 5.6327959, "lon": -0.1815276}, {"type": "node", "id": 11915087766, "lat": 5.6328385, "lon": -0.1815386}, {"type": "node", "id": 11915087767, "lat": 5.6328415, "lon": -0.1815276}, {"type": "node", "id": 11915087768, "lat": 5.6328008, "lon": -0.1815156}, {"type": "node", "id": 11915087769, "lat": 5.6328276, "lon": -0.1814249}, {"type": "node", "id": 11915087770, "lat": 5.6328703, "lon": -0.1814359}, {"type": "node", "id": 11915087771, "lat": 5.6326927, "lon": -0.1818117}, {"type": "node", "id": 11915087772, "lat": 5.632652, "lon": -0.1817997}, {"type": "node", "id": 11915087773, "lat": 5.6326788, "lon": -0.181709}, {"type": "node", "id": 11915087774, "lat": 5.6327215, "lon": -0.18172}, {"type": "node", "id": 11915087775, "lat": 5.6327264, "lon": -0.18171}, {"type": "node", "id": 11915087776, "lat": 5.6326857, "lon": -0.1816981}, {"type": "node", "id": 11915087777, "lat": 5.6327125, "lon": -0.1816074}, {"type": "node", "id": 11915087778, "lat": 5.6327552, "lon": -0.1816183}, {"type": "node", "id": 11915087779, "lat": 5.6327601, "lon": -0.1816123}, {"type": "node", "id": 11915087780, "lat": 5.6327195, "lon": -0.1816004}, {"type": "node", "id": 11915087781, "lat": 5.6327463, "lon": -0.1815097}, {"type": "node", "id": 11915087782, "lat": 5.6327889, "lon": -0.1815206}, {"type": "node", "id": 11915087783, "lat": 5.6327919, "lon": -0.1815097}, {"type": "node", "id": 11915087784, "lat": 5.6327512, "lon": -0.1814977}, {"type": "node", "id": 11915087785, "lat": 5.632778, "lon": -0.181407}, {"type": "node", "id": 11915087786, "lat": 5.6328207, "lon": -0.181418}, {"type": "way", "id": 651677692, "nodes": [6110612516, 6110612515, 6110612514, 6110612513, 6110612516], "tags": {"building": "yes", "generator:source": "solar", "power": "generator"}}, {"type": "way", "id": 672764755, "nodes": [6300118420, 6300118421, 6300118422, 6300118423, 6300118420], "tags": {"generator:source": "oil", "power": "generator"}}, {"type": "way", "id": 750035357, "nodes": [7013543057, 7013543056, 7013543055, 7013543054, 7013543057], "tags": {"fixme": "field validate", "generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "location": "roof", "power": "generator"}}, {"type": "way", "id": 1211809383, "nodes": [11226871362, 11226871363, 11226871364, 11226871365, 11226871362], "tags": {"power": "generator"}}, {"type": "way", "id": 1284323409, "nodes": [11915075279, 11915075280, 11915075281, 11915075282, 11915075279], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323410, "nodes": [11915075283, 11915075284, 11915075285, 11915075286, 11915075283], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323411, "nodes": [11915075287, 11915075288, 11915075289, 11915075290, 11915075287], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323412, "nodes": [11915075291, 11915075292, 11915075293, 11915075294, 11915075291], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323413, "nodes": [11915075295, 11915075296, 11915075297, 11915075298, 11915075295], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323414, "nodes": [11915075299, 11915075300, 11915075301, 11915075302, 11915075299], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323415, "nodes": [11915075303, 11915075304, 11915075305, 11915075306, 11915075303], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323416, "nodes": [11915075307, 11915075308, 11915075309, 11915075310, 11915075307], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323417, "nodes": [11915075311, 11915075312, 11915075313, 11915075314, 11915075311], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323418, "nodes": [11915075315, 11915075316, 11915075317, 11915075318, 11915075315], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323419, "nodes": [11915075319, 11915075320, 11915075321, 11915075322, 11915075319], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323420, "nodes": [11915075323, 11915075324, 11915075325, 11915075326, 11915075323], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323421, "nodes": [11915075327, 11915075328, 11915075329, 11915075330, 11915075327], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323422, "nodes": [11915075331, 11915075332, 11915075333, 11915075334, 11915075331], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323423, "nodes": [11915075335, 11915075336, 11915075337, 11915075338, 11915075335], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323424, "nodes": [11915075339, 11915075340, 11915075341, 11915075342, 11915075339], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323425, "nodes": [11915075343, 11915075344, 11915075345, 11915075346, 11915075343], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323426, "nodes": [11915075347, 11915075348, 11915075349, 11915075350, 11915075347], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323427, "nodes": [11915075351, 11915075352, 11915075353, 11915075354, 11915075351], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323428, "nodes": [11915075355, 11915075356, 11915075357, 11915075358, 11915075355], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323429, "nodes": [11915075359, 11915075360, 11915075361, 11915075362, 11915075359], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323430, "nodes": [11915075363, 11915075364, 11915075365, 11915075366, 11915075363], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323431, "nodes": [11915075367, 11915075368, 11915087369, 11915087370, 11915075367], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323432, "nodes": [11915087371, 11915087372, 11915087373, 11915087374, 11915087371], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323433, "nodes": [11915087375, 11915087376, 11915087377, 11915087378, 11915087375], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323434, "nodes": [11915087379, 11915087380, 11915087381, 11915087382, 11915087379], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323435, "nodes": [11915087383, 11915087384, 11915087385, 11915087386, 11915087383], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323436, "nodes": [11915087387, 11915087388, 11915087389, 11915087390, 11915087387], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323437, "nodes": [11915087391, 11915087392, 11915087393, 11915087394, 11915087391], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323438, "nodes": [11915087395, 11915087396, 11915087397, 11915087398, 11915087395], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323439, "nodes": [11915087399, 11915087400, 11915087401, 11915087402, 11915087399], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323440, "nodes": [11915087403, 11915087404, 11915087405, 11915087406, 11915087403], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323441, "nodes": [11915087407, 11915087408, 11915087409, 11915087410, 11915087407], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323442, "nodes": [11915087411, 11915087412, 11915087413, 11915087414, 11915087411], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323444, "nodes": [11915087419, 11915087420, 11915087421, 11915087422, 11915087419], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323445, "nodes": [11915087423, 11915087424, 11915087425, 11915087426, 11915087423], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323446, "nodes": [11915087427, 11915087428, 11915087429, 11915087430, 11915087427], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323447, "nodes": [11915087431, 11915087432, 11915087433, 11915087434, 11915087431], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323448, "nodes": [11915087435, 11915087436, 11915087437, 11915087438, 11915087435], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323449, "nodes": [11915087439, 11915087440, 11915087441, 11915087442, 11915087439], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323450, "nodes": [11915087443, 11915087444, 11915087445, 11915087446, 11915087443], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323451, "nodes": [11915087447, 11915087448, 11915087449, 11915087450, 11915087447], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323452, "nodes": [11915087451, 11915087452, 11915087453, 11915087454, 11915087451], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323453, "nodes": [11915087455, 11915087456, 11915087457, 11915087458, 11915087455], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323454, "nodes": [11915087459, 11915087460, 11915087461, 11915087462, 11915087459], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323455, "nodes": [11915087463, 11915087464, 11915087465, 11915087466, 11915087463], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323456, "nodes": [11915087467, 11915087468, 11915087469, 11915087470, 11915087467], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323457, "nodes": [11915087471, 11915087472, 11915087473, 11915087474, 11915087471], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323458, "nodes": [11915087475, 11915087476, 11915087477, 11915087478, 11915087475], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323459, "nodes": [11915087479, 11915087480, 11915087481, 11915087482, 11915087479], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323460, "nodes": [11915087483, 11915087484, 11915087485, 11915087486, 11915087483], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323461, "nodes": [11915087487, 11915087488, 11915087489, 11915087490, 11915087487], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323462, "nodes": [11915087491, 11915087492, 11915087493, 11915087494, 11915087491], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323463, "nodes": [11915087495, 11915087496, 11915087497, 11915087498, 11915087495], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323464, "nodes": [11915087499, 11915087500, 11915087501, 11915087502, 11915087499], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323465, "nodes": [11915087503, 11915087504, 11915087505, 11915087506, 11915087503], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323466, "nodes": [11915087507, 11915087508, 11915087509, 11915087510, 11915087507], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323467, "nodes": [11915087511, 11915087512, 11915087513, 11915087514, 11915087511], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323468, "nodes": [11915087515, 11915087516, 11915087517, 11915087518, 11915087515], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323469, "nodes": [11915087519, 11915087520, 11915087521, 11915087522, 11915087519], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323470, "nodes": [11915087523, 11915087524, 11915087525, 11915087526, 11915087523], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323471, "nodes": [11915087527, 11915087528, 11915087529, 11915087530, 11915087527], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323472, "nodes": [11915087531, 11915087532, 11915087533, 11915087534, 11915087531], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323473, "nodes": [11915087535, 11915087536, 11915087537, 11915087538, 11915087535], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323474, "nodes": [11915087539, 11915087540, 11915087541, 11915087542, 11915087539], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323475, "nodes": [11915087543, 11915087544, 11915087545, 11915087546, 11915087543], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323476, "nodes": [11915087547, 11915087548, 11915087549, 11915087550, 11915087547], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323477, "nodes": [11915087551, 11915087552, 11915087553, 11915087554, 11915087551], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323478, "nodes": [11915087555, 11915087556, 11915087557, 11915087558, 11915087555], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323479, "nodes": [11915087559, 11915087560, 11915087561, 11915087562, 11915087559], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323480, "nodes": [11915087563, 11915087564, 11915087565, 11915087566, 11915087563], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323481, "nodes": [11915087567, 11915087568, 11915087569, 11915087570, 11915087567], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323482, "nodes": [11915087571, 11915087572, 11915087573, 11915087574, 11915087571], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323483, "nodes": [11915087575, 11915087576, 11915087577, 11915087578, 11915087575], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323484, "nodes": [11915087579, 11915087580, 11915087581, 11915087582, 11915087579], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323485, "nodes": [11915087583, 11915087584, 11915087585, 11915087586, 11915087583], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323486, "nodes": [11915087587, 11915087588, 11915087589, 11915087590, 11915087587], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323487, "nodes": [11915087591, 11915087592, 11915087593, 11915087594, 11915087591], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323488, "nodes": [11915087595, 11915087596, 11915087597, 11915087598, 11915087595], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323489, "nodes": [11915087599, 11915087600, 11915087601, 11915087602, 11915087599], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323490, "nodes": [11915087603, 11915087604, 11915087605, 11915087606, 11915087603], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323491, "nodes": [11915087607, 11915087608, 11915087609, 11915087610, 11915087607], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323492, "nodes": [11915087611, 11915087612, 11915087613, 11915087614, 11915087611], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323493, "nodes": [11915087615, 11915087616, 11915087617, 11915087618, 11915087615], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323494, "nodes": [11915087619, 11915087620, 11915087621, 11915087622, 11915087619], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323495, "nodes": [11915087623, 11915087624, 11915087625, 11915087626, 11915087623], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323496, "nodes": [11915087627, 11915087628, 11915087629, 11915087630, 11915087627], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323497, "nodes": [11915087631, 11915087632, 11915087633, 11915087634, 11915087631], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323498, "nodes": [11915087635, 11915087636, 11915087637, 11915087638, 11915087635], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323499, "nodes": [11915087639, 11915087640, 11915087641, 11915087642, 11915087639], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323500, "nodes": [11915087643, 11915087644, 11915087645, 11915087646, 11915087643], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323501, "nodes": [11915087647, 11915087648, 11915087649, 11915087650, 11915087647], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323502, "nodes": [11915087651, 11915087652, 11915087653, 11915087654, 11915087651], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323503, "nodes": [11915087655, 11915087656, 11915087657, 11915087658, 11915087655], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323504, "nodes": [11915087659, 11915087660, 11915087661, 11915087662, 11915087659], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323505, "nodes": [11915087663, 11915087664, 11915087665, 11915087666, 11915087663], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323506, "nodes": [11915087667, 11915087668, 11915087669, 11915087670, 11915087667], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323507, "nodes": [11915087671, 11915087672, 11915087673, 11915087674, 11915087671], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323508, "nodes": [11915087675, 11915087676, 11915087677, 11915087678, 11915087675], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323509, "nodes": [11915087679, 11915087680, 11915087681, 11915087682, 11915087679], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323510, "nodes": [11915087683, 11915087684, 11915087685, 11915087686, 11915087683], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323511, "nodes": [11915087687, 11915087688, 11915087689, 11915087690, 11915087687], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323512, "nodes": [11915087691, 11915087692, 11915087693, 11915087694, 11915087691], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323513, "nodes": [11915087695, 11915087696, 11915087697, 11915087698, 11915087695], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323514, "nodes": [11915087699, 11915087700, 11915087701, 11915087702, 11915087699], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323515, "nodes": [11915087703, 11915087704, 11915087705, 11915087706, 11915087703], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323516, "nodes": [11915087707, 11915087708, 11915087709, 11915087710, 11915087707], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323517, "nodes": [11915087711, 11915087712, 11915087713, 11915087714, 11915087711], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323518, "nodes": [11915087715, 11915087716, 11915087717, 11915087718, 11915087715], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323519, "nodes": [11915087719, 11915087720, 11915087721, 11915087722, 11915087719], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323520, "nodes": [11915087723, 11915087724, 11915087725, 11915087726, 11915087723], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323521, "nodes": [11915087727, 11915087728, 11915087729, 11915087730, 11915087727], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323522, "nodes": [11915087731, 11915087732, 11915087733, 11915087734, 11915087731], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323523, "nodes": [11915087735, 11915087736, 11915087737, 11915087738, 11915087735], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323524, "nodes": [11915087739, 11915087740, 11915087741, 11915087742, 11915087739], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323525, "nodes": [11915087743, 11915087744, 11915087745, 11915087746, 11915087743], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323526, "nodes": [11915087747, 11915087748, 11915087749, 11915087750, 11915087747], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323527, "nodes": [11915087751, 11915087752, 11915087753, 11915087754, 11915087751], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323528, "nodes": [11915087755, 11915087756, 11915087757, 11915087758, 11915087755], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323529, "nodes": [11915087759, 11915087760, 11915087761, 11915087762, 11915087759], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323530, "nodes": [11915087763, 11915087764, 11915087765, 11915087766, 11915087763], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323531, "nodes": [11915087767, 11915087768, 11915087769, 11915087770, 11915087767], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323532, "nodes": [11915087771, 11915087772, 11915087773, 11915087774, 11915087771], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323533, "nodes": [11915087775, 11915087776, 11915087777, 11915087778, 11915087775], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323534, "nodes": [11915087779, 11915087780, 11915087781, 11915087782, 11915087779], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323535, "nodes": [11915087783, 11915087784, 11915087785, 11915087786, 11915087783], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}, {"type": "way", "id": 1284323821, "nodes": [11915071926, 11915071927, 11915071928, 11915071929, 11915071926], "tags": {"generator:method": "photovoltaic", "generator:output:electricity": "yes", "generator:source": "solar", "generator:type": "solar_photovoltaic_panel", "power": "generator"}}]}