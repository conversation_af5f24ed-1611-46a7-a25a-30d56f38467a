{"version": 0.6, "generator": "Overpass API ******** 375dc00a", "osm3s": {"timestamp_osm_base": "2025-06-30T13:07:15Z", "copyright": "The data included in this document is from www.openstreetmap.org. The data is made available under ODbL."}, "elements": [{"type": "node", "id": 107319420, "lat": 5.5659577, "lon": -0.1807053, "tags": {"name": "Koala Shopping Centre", "shop": "supermarket"}}, {"type": "node", "id": 493756516, "lat": 5.6216545, "lon": -0.1737286, "tags": {"brand": "Shoprite", "brand:wikidata": "Q1857639", "brand:wikipedia": "en:Shoprite (retailer)", "check_date:opening_hours": "2023-01-23", "level": "0", "name": "Shoprite", "opening_hours": "Mo-Su 09:00-21:00", "phone": "+233302823020", "shop": "supermarket", "wheelchair": "yes"}}, {"type": "node", "id": 1175333219, "lat": 5.6029681, "lon": -0.1860135, "tags": {"name": "Ridge Stop Over Supermarket", "shop": "supermarket"}}, {"type": "node", "id": 1183555461, "lat": 5.591253, "lon": -0.1804471, "tags": {"air_conditioning": "yes", "check_date": "2024-01-08", "currency:GHS": "yes", "diet:halal": "yes", "name": "Maxmart Shopping Center", "name:it": "Maxmart", "organic": "yes", "second_hand": "no", "shop": "supermarket", "source": "local knowledge", "stroller": "yes", "toilets": "yes", "toilets:wheelchair": "limited", "wheelchair": "limited"}}, {"type": "node", "id": 1276048666, "lat": 5.6081047, "lon": -0.1789897, "tags": {"name": "Diplomatic Shop", "shop": "supermarket"}}, {"type": "node", "id": 1276388996, "lat": 5.5851466, "lon": -0.2221003, "tags": {"name": "Melcom Plus", "shop": "supermarket"}}, {"type": "node", "id": 1390293382, "lat": 5.546819, "lon": -0.2062538, "tags": {"name": "Makola Shoping Mail", "shop": "supermarket"}}, {"type": "node", "id": 1492883357, "lat": 5.6225574, "lon": -0.1729668, "tags": {"name": "Game", "not:brand:wikidata": "Q126811048", "opening_hours": "Mo-Sa 09:00-21:00; Su,PH 10:00-19:00", "phone": "+2333022740000", "shop": "supermarket", "wheelchair": "yes"}}, {"type": "node", "id": 1631117186, "lat": 5.5554931, "lon": -0.1822639, "tags": {"name": "Lara Mart", "shop": "supermarket"}}, {"type": "node", "id": 1839965810, "lat": 5.6094995, "lon": -0.1840525, "tags": {"name": "<PERSON><PERSON>", "shop": "supermarket"}}, {"type": "node", "id": 1861364524, "lat": 5.6092057, "lon": -0.1826349, "tags": {"name": "Koala Shopping Centre", "shop": "supermarket"}}, {"type": "node", "id": 2007310259, "lat": 5.571494, "lon": -0.115303}, {"type": "node", "id": 2007310261, "lat": 5.5716502, "lon": -0.1150298}, {"type": "node", "id": 2007310264, "lat": 5.571755, "lon": -0.1154527}, {"type": "node", "id": 2007310268, "lat": 5.5718479, "lon": -0.1151458}, {"type": "node", "id": 2007310273, "lat": 5.5719098, "lon": -0.1151802}, {"type": "node", "id": 2026403686, "lat": 5.5311141, "lon": -0.2635665, "tags": {"name": "Lomnava Super Market", "opening_hours": "Mo-Su 08:00-22:00", "shop": "supermarket"}}, {"type": "node", "id": 2068163030, "lat": 5.653338, "lon": -0.1790408, "tags": {"name": "Max Mart", "shop": "supermarket"}}, {"type": "node", "id": 2431972085, "lat": 5.5604248, "lon": -0.1823765, "tags": {"brand": "Shoprite", "brand:wikidata": "Q1857639", "brand:wikipedia": "en:Shoprite (retailer)", "name": "Shoprite", "shop": "supermarket", "wheelchair": "yes"}}, {"type": "node", "id": 2574758185, "lat": 5.5599028, "lon": -0.2596966}, {"type": "node", "id": 2574758186, "lat": 5.5597272, "lon": -0.2596749}, {"type": "node", "id": 2574758191, "lat": 5.5597688, "lon": -0.2593355}, {"type": "node", "id": 2574758192, "lat": 5.5599445, "lon": -0.2593572}, {"type": "node", "id": 2666879971, "lat": 5.5675562, "lon": -0.1805342, "tags": {"name": "Great Wall Supermarket", "shop": "supermarket"}}, {"type": "node", "id": 3037520188, "lat": 5.602386, "lon": -0.1905363, "tags": {"name": "Flemingo Duty Free Shop", "shop": "supermarket"}}, {"type": "node", "id": 3070233497, "lat": 5.5866632, "lon": -0.2089728, "tags": {"name": "Night Market", "shop": "supermarket"}}, {"type": "node", "id": 3073613435, "lat": 5.5679453, "lon": -0.2429632, "tags": {"name": "Cash Down", "shop": "supermarket"}}, {"type": "node", "id": 3095490652, "lat": 5.5434312, "lon": -0.2069344, "tags": {"brand": "Melcom", "brand:wikidata": "Q6812065", "name": "Melcom", "shop": "supermarket"}}, {"type": "node", "id": 3154394318, "lat": 5.605462, "lon": -0.2591357, "tags": {"name": "Modex Supermarket", "shop": "supermarket"}}, {"type": "node", "id": 3595142827, "lat": 5.6426586, "lon": -0.1861684}, {"type": "node", "id": 3595142828, "lat": 5.6426605, "lon": -0.1860704}, {"type": "node", "id": 3595142829, "lat": 5.6429827, "lon": -0.1861747}, {"type": "node", "id": 3595142830, "lat": 5.6429846, "lon": -0.1860767}, {"type": "node", "id": 3667771291, "lat": 5.5648746, "lon": -0.1821077, "tags": {"name": "Saagar Supermarket", "shop": "supermarket"}}, {"type": "node", "id": 3751562725, "lat": 5.6143396, "lon": -0.2029959, "tags": {"name": "Max Mart", "shop": "supermarket"}}, {"type": "node", "id": 3986237938, "lat": 5.5940109, "lon": -0.0935834, "tags": {"name": "A-Life Supermarket", "shop": "supermarket"}}, {"type": "node", "id": 4231748740, "lat": 5.611786, "lon": -0.1985926, "tags": {"name": "Novelty supermarket", "shop": "supermarket"}}, {"type": "node", "id": 4305848903, "lat": 5.5570833, "lon": -0.2230628, "tags": {"check_date": "2023-04-21", "name": "Yoo! Mart", "payment:cash": "yes", "shop": "supermarket", "wheelchair": "limited"}}, {"type": "node", "id": 4349001983, "lat": 5.6372689, "lon": -0.1500005, "tags": {"name": "Zambang Mart", "shop": "supermarket"}}, {"type": "node", "id": 4351837559, "lat": 5.6361837, "lon": -0.2156387, "tags": {"brand": "Melcom", "brand:wikidata": "Q6812065", "name": "Melcom", "shop": "supermarket"}}, {"type": "node", "id": 4443298589, "lat": 5.5594993, "lon": -0.2597407, "tags": {"name": "stus supermarket", "name:en": "stus supermarket", "shop": "supermarket"}}, {"type": "node", "id": 4462915136, "lat": 5.624737, "lon": -0.1540598}, {"type": "node", "id": 4462915137, "lat": 5.6247872, "lon": -0.1542479}, {"type": "node", "id": 4462915138, "lat": 5.6248158, "lon": -0.1540727}, {"type": "node", "id": 4462915139, "lat": 5.6248331, "lon": -0.1534702}, {"type": "node", "id": 4462915140, "lat": 5.6249204, "lon": -0.1534846}, {"type": "node", "id": 4462915141, "lat": 5.6249402, "lon": -0.1533631}, {"type": "node", "id": 4462915144, "lat": 5.6254179, "lon": -0.1543516}, {"type": "node", "id": 4462915145, "lat": 5.6254256, "lon": -0.1543047}, {"type": "node", "id": 4462915146, "lat": 5.6256527, "lon": -0.1543421}, {"type": "node", "id": 4462915147, "lat": 5.6257894, "lon": -0.1535027}, {"type": "node", "id": 4462915149, "lat": 5.6260297, "lon": -0.1514781}, {"type": "node", "id": 4462915150, "lat": 5.6261373, "lon": -0.1511445}, {"type": "node", "id": 4462915152, "lat": 5.6262075, "lon": -0.1509451}, {"type": "node", "id": 4462915160, "lat": 5.6267187, "lon": -0.1517025}, {"type": "node", "id": 4462915164, "lat": 5.6268388, "lon": -0.1513742}, {"type": "node", "id": 4462915165, "lat": 5.6269042, "lon": -0.1511623}, {"type": "node", "id": 4462915526, "lat": 5.6288042, "lon": -0.1492266}, {"type": "node", "id": 4462915530, "lat": 5.6289716, "lon": -0.1487066}, {"type": "node", "id": 4462915542, "lat": 5.6294577, "lon": -0.149439}, {"type": "node", "id": 4462915543, "lat": 5.629466, "lon": -0.1494131}, {"type": "node", "id": 4462915546, "lat": 5.629561, "lon": -0.1491016}, {"type": "node", "id": 4462915547, "lat": 5.6296203, "lon": -0.1489175}, {"type": "node", "id": 4462915549, "lat": 5.6298064, "lon": -0.1491814}, {"type": "node", "id": 4462915551, "lat": 5.6298548, "lon": -0.149031}, {"type": "node", "id": 4462915562, "lat": 5.6304614, "lon": -0.1493961}, {"type": "node", "id": 4462915564, "lat": 5.6305103, "lon": -0.1492441}, {"type": "node", "id": 4462915567, "lat": 5.63065, "lon": -0.149798}, {"type": "node", "id": 4462915568, "lat": 5.6307493, "lon": -0.1494897}, {"type": "node", "id": 4526082589, "lat": 5.6247371, "lon": -0.0782628, "tags": {"shop": "supermarket"}}, {"type": "node", "id": 4530665289, "lat": 5.5894144, "lon": -0.106381, "tags": {"fixme": "move to exact location", "name": "Kairos Business Centre", "shop": "supermarket"}}, {"type": "node", "id": 4581488632, "lat": 5.5790336, "lon": -0.2317393, "tags": {"check_date": "2023-05-09", "name": "GhanaMart Pickup depot", "opening_hours": "Mo-Fr 10:00-18:00", "operator": "Ghanamart Inc.", "payment:cash": "yes", "phone": "+233 302244301", "shop": "supermarket", "website": "https://www.ghanamart.com/", "wheelchair": "limited"}}, {"type": "node", "id": 4588855257, "lat": 5.6168063, "lon": -0.1012824, "tags": {"name": "Freedom Supermarket", "shop": "supermarket"}}, {"type": "node", "id": 4699262792, "lat": 5.575228, "lon": -0.139185, "tags": {"name": "Container", "shop": "supermarket"}}, {"type": "node", "id": 4830568667, "lat": 5.5932267, "lon": -0.09076, "tags": {"name": "Citydia Supermarket", "shop": "supermarket"}}, {"type": "node", "id": 4830568675, "lat": 5.6129059, "lon": -0.071272, "tags": {"brand": "Shoprite", "brand:wikidata": "Q1857639", "brand:wikipedia": "en:Shoprite (retailer)", "name": "Shoprite", "shop": "supermarket"}}, {"type": "node", "id": **********, "lat": 5.5875424, "lon": -0.0998912}, {"type": "node", "id": **********, "lat": 5.5876261, "lon": -0.0997601}, {"type": "node", "id": **********, "lat": 5.5876406, "lon": -0.1000501}, {"type": "node", "id": **********, "lat": 5.5878777, "lon": -0.0999224}, {"type": "node", "id": **********, "lat": 5.5877507, "lon": -0.1001211}, {"type": "node", "id": 4881657500, "lat": 5.575552, "lon": -0.1383591, "tags": {"name": "Royal Mart", "shop": "supermarket"}}, {"type": "node", "id": 4884077521, "lat": 5.5772797, "lon": -0.1651609, "tags": {"name": "Koala Supermarket", "name:en": "Koala Supermarket", "shop": "supermarket"}}, {"type": "node", "id": 4917253677, "lat": 5.5552947, "lon": -0.1704913, "tags": {"name": "City Dia", "shop": "supermarket"}}, {"type": "node", "id": **********, "lat": 5.6479084, "lon": -0.1789957}, {"type": "node", "id": **********, "lat": 5.6479647, "lon": -0.1785558}, {"type": "node", "id": **********, "lat": 5.6481635, "lon": -0.1785815}, {"type": "node", "id": **********, "lat": 5.6481258, "lon": -0.1788759}, {"type": "node", "id": **********, "lat": 5.6481847, "lon": -0.1788835}, {"type": "node", "id": **********, "lat": 5.6481735, "lon": -0.1789709}, {"type": "node", "id": **********, "lat": 5.6481609, "lon": -0.1789693}, {"type": "node", "id": **********, "lat": 5.6481513, "lon": -0.1790444}, {"type": "node", "id": 4991168452, "lat": 5.603432, "lon": -0.2431205, "tags": {"brand": "Melcom", "brand:wikidata": "Q6812065", "name": "Melcom", "shop": "supermarket"}}, {"type": "node", "id": 5746104420, "lat": 5.5581685, "lon": -0.1630831, "tags": {"brand": "Melcom", "brand:wikidata": "Q6812065", "name": "Melcom", "shop": "supermarket"}}, {"type": "node", "id": **********, "lat": 5.6269528, "lon": -0.2345478}, {"type": "node", "id": **********, "lat": 5.6268751, "lon": -0.2346806}, {"type": "node", "id": **********, "lat": 5.6267871, "lon": -0.2346287}, {"type": "node", "id": **********, "lat": 5.6267933, "lon": -0.2346179}, {"type": "node", "id": **********, "lat": 5.6267316, "lon": -0.2345815}, {"type": "node", "id": **********, "lat": 5.6267709, "lon": -0.2345143}, {"type": "node", "id": **********, "lat": 5.6268495, "lon": -0.2345606}, {"type": "node", "id": **********, "lat": 5.6268815, "lon": -0.2345058}, {"type": "node", "id": 5910232098, "lat": 5.645742, "lon": -0.149309, "tags": {"name": "<PERSON><PERSON><PERSON>", "shop": "supermarket"}}, {"type": "node", "id": 5955241387, "lat": 5.5798766, "lon": -0.2290499, "tags": {"name": "<PERSON><PERSON><PERSON>", "shop": "supermarket"}}, {"type": "node", "id": **********, "lat": 5.6158088, "lon": -0.245164}, {"type": "node", "id": **********, "lat": 5.6157477, "lon": -0.2451885}, {"type": "node", "id": **********, "lat": 5.6157418, "lon": -0.2451737}, {"type": "node", "id": **********, "lat": 5.6158029, "lon": -0.2451491}, {"type": "node", "id": **********, "lat": 5.6157041, "lon": -0.2451888}, {"type": "node", "id": **********, "lat": 5.6156654, "lon": -0.2450916}, {"type": "node", "id": **********, "lat": 5.615829, "lon": -0.2450258}, {"type": "node", "id": **********, "lat": 5.6158677, "lon": -0.2451231}, {"type": "node", "id": 5969633907, "lat": 5.6218096, "lon": -0.2307967, "tags": {"addr:city": "Accra", "addr:community": "Achimota Overhead", "addr:suburb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Goil Mart", "shop": "supermarket", "source": "Open Cities Accra - Field Survey"}}, {"type": "node", "id": 6100970621, "lat": 5.6082075, "lon": -0.2076627, "tags": {"name": "Citydia", "shop": "supermarket"}}, {"type": "node", "id": 6289047074, "lat": 5.5702008, "lon": -0.2218195, "tags": {"check_date": "2023-05-11", "name": "Citydia Supermarket", "payment:cash": "yes", "second_hand": "no", "shop": "supermarket", "stroller": "yes", "wheelchair": "limited"}}, {"type": "node", "id": 6311363201, "lat": 5.5834554, "lon": -0.2709649}, {"type": "node", "id": 6311363202, "lat": 5.5833772, "lon": -0.2709528}, {"type": "node", "id": 6311363203, "lat": 5.5833862, "lon": -0.2708954}, {"type": "node", "id": 6311363204, "lat": 5.5834133, "lon": -0.2709135}, {"type": "node", "id": 6311363205, "lat": 5.5834238, "lon": -0.2709286}, {"type": "node", "id": 6311363206, "lat": 5.5834389, "lon": -0.2709165}, {"type": "node", "id": 6311363207, "lat": 5.5834585, "lon": -0.2709196}, {"type": "node", "id": 6387882565, "lat": 5.5731932, "lon": -0.1658417, "tags": {"name": "Fairway Mini Market", "opening_hours": "Mo-Sa 08:00-21:00; Su 09:00-21:00", "shop": "supermarket"}}, {"type": "node", "id": 6528488906, "lat": 5.6305546, "lon": -0.1730084, "tags": {"name": "Citydia", "shop": "supermarket"}}, {"type": "node", "id": 6720099145, "lat": 5.6376046, "lon": -0.1844282, "tags": {"shop": "supermarket"}}, {"type": "node", "id": 6720099146, "lat": 5.637276, "lon": -0.1849169, "tags": {"shop": "supermarket"}}, {"type": "node", "id": 6720099147, "lat": 5.6369664, "lon": -0.1855338, "tags": {"shop": "supermarket"}}, {"type": "node", "id": 6720099148, "lat": 5.6366621, "lon": -0.1860327, "tags": {"shop": "supermarket"}}, {"type": "node", "id": 6746230483, "lat": 5.6272628, "lon": -0.0873648, "tags": {"name": "Baatsona Total", "shop": "supermarket"}}, {"type": "node", "id": **********, "lat": 5.5876838, "lon": -0.0999824}, {"type": "node", "id": 6937678140, "lat": 5.5687231, "lon": -0.1746329, "tags": {"name": "Finest", "opening_hours": "Mo-Su 07:00-21:00", "shop": "supermarket"}}, {"type": "node", "id": 6975166265, "lat": 5.5815721, "lon": -0.1984415, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Al-Waleed Bin-Talal Highway", "addr:suburb": "Nima Market", "opening_hours": "Mo-Sa 08:00-18:00", "shop": "supermarket", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": 6975166331, "lat": 5.5808797, "lon": -0.1986346, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:suburb": "Nima Market", "opening_hours": "Mo-Sa 08:00-18:00", "shop": "supermarket", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": 6983629824, "lat": 5.5793187, "lon": -0.2000713, "tags": {"addr:city": "Accra", "addr:community": "<PERSON><PERSON>", "addr:street": "Al-Waleed Bin-Talal Highway", "addr:suburb": "Nima Market", "name": "Lairms", "phone": "+233249390008", "shop": "supermarket", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "node", "id": 7019865781, "lat": 5.5614533, "lon": -0.2134441, "tags": {"name": "Citydia", "shop": "supermarket"}}, {"type": "node", "id": 7033934885, "lat": 5.6162301, "lon": -0.2291362, "tags": {"name": "ka<PERSON><PERSON><PERSON>gh Achimota", "shop": "supermarket"}}, {"type": "node", "id": 7147443580, "lat": 5.6376514, "lon": -0.1844502, "tags": {"name": "Samsung shop", "opening_hours": "Mo-Su 08:00-22:00", "payment:cash": "yes", "shop": "supermarket"}}, {"type": "node", "id": 9429528428, "lat": 5.5996845, "lon": -0.0806931, "tags": {"name": "<PERSON><PERSON>", "opening_hours": "Mo-Su 07:00-19:00", "operator": "<PERSON><PERSON>", "phone": "233 59 385 7581", "shop": "supermarket"}}, {"type": "node", "id": 9447858187, "lat": 5.6132586, "lon": -0.0715458, "tags": {"name": "Divine Organic Hub", "opening_hours": "Mo-Su 09:00-21:00", "organic": "only", "phone": "233555567027", "shop": "supermarket", "website": "https://www.divineorganichubghana.com"}}, {"type": "node", "id": 10280126883, "lat": 5.5987351, "lon": -0.2295501, "tags": {"name": "Marina Supermarket", "shop": "supermarket"}}, {"type": "node", "id": 11092523261, "lat": 5.647971, "lon": -0.1790211}, {"type": "node", "id": 11092523262, "lat": 5.6479732, "lon": -0.1790041}, {"type": "way", "id": 190125152, "nodes": [2007310268, 2007310273, 2007310264, 2007310259, 2007310261, 2007310268], "tags": {"building": "yes", "name": "Rice & Suggar Supermarket", "shop": "supermarket"}}, {"type": "way", "id": 251224454, "nodes": [2574758185, 2574758186, 2574758191, 2574758192, 2574758185], "tags": {"brand": "Melcom", "brand:wikidata": "Q6812065", "building": "yes", "name": "Melcom", "shop": "supermarket", "website": "https://www.melcomgroup.com/index_2.html", "wikipedia": "en:Melcom"}}, {"type": "way", "id": 353697483, "nodes": [3595142829, 3595142830, 3595142828, 3595142827, 3595142829], "tags": {"name": "All Needs Supermarket", "shop": "supermarket"}}, {"type": "way", "id": 449320967, "nodes": [4462915160, 4462915164, 4462915150, 4462915149, 4462915160], "tags": {"addr:street": "Spintex Road", "building": "yes", "name": "Melcom Plus", "shop": "supermarket"}}, {"type": "way", "id": 449320969, "nodes": [4462915146, 4462915147, 4462915141, 4462915140, 4462915139, 4462915136, 4462915138, 4462915137, 4462915144, 4462915145, 4462915146], "tags": {"building": "yes", "name": "Palace Hypermarket", "name:it": "Palace", "shop": "supermarket"}}, {"type": "way", "id": 449320970, "nodes": [4462915567, 4462915568, 4462915562, 4462915564, 4462915551, 4462915549, 4462915546, 4462915547, 4462915530, 4462915526, 4462915542, 4462915543, 4462915567], "tags": {"addr:street": "Spintex Road", "building": "yes", "name": "Palace Hypermarket", "name:it": "Palace", "shop": "supermarket"}}, {"type": "way", "id": 449320971, "nodes": [4462915164, 4462915165, 4462915152, 4462915150, 4462915164], "tags": {"addr:street": "Spintex Road", "building": "yes", "name": "Sneda Shopping", "name:it": "Sneda", "shop": "supermarket"}}, {"type": "way", "id": 490882246, "nodes": [**********, **********, **********, **********, **********, **********, **********], "tags": {"alt_name": "Melcom Teshie", "branch": "<PERSON><PERSON><PERSON>", "brand": "Melcom", "brand:wikidata": "Q6812065", "building": "yes", "name": "Melcom", "shop": "supermarket"}}, {"type": "way", "id": 503106587, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, 11092523262, 11092523261, **********], "tags": {"building": "yes", "name": "family health shio", "shop": "supermarket"}}, {"type": "way", "id": 608924176, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, **********], "tags": {"addr:city": "Accra", "addr:community": "ABC", "addr:street": "J.A. <PERSON>our Avenue", "addr:suburb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "building": "commercial", "building:levels": "1", "building:material": "metal;concrete;cement_block", "roof:material": "concrete", "shop": "supermarket", "source": "Open Cities Accra - Field Survey"}}, {"type": "way", "id": 632237571, "nodes": [**********, **********, **********, **********, **********, **********, **********, **********, **********], "tags": {"addr:city": "Accra", "addr:community": "Nii Boi Town", "addr:street": "Taki <PERSON>", "addr:suburb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "building": "commercial", "building:levels": "2", "building:material": "cement_block", "roof:material": "metal", "shop": "supermarket", "source": "Open Cities Accra / GARID- Field Survey"}}, {"type": "way", "id": 673938172, "nodes": [6311363201, 6311363202, 6311363203, 6311363204, 6311363205, 6311363206, 6311363207, 6311363201], "tags": {"building": "yes", "name": "Mama <PERSON> Enterprise", "opening_hours": "sunrise-sunset", "shop": "supermarket"}}]}