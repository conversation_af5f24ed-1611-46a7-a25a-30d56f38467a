#!/usr/bin/env python3
"""
Simple script to display all locations in Accra, Ghana in a clean format
"""

import json

def load_locations():
    """Load locations from JSON file"""
    with open('accra_locations.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def display_all_locations():
    """Display all locations organized by category"""
    data = load_locations()
    
    print("=" * 60)
    print(f"LOCATIONS IN ACCRA, GHANA")
    print("=" * 60)
    print(f"Total Locations: {data['total_locations']}")
    print(f"Last Updated: {data['last_updated']}")
    print("=" * 60)
    
    for category_name, locations in data['categories'].items():
        category_title = category_name.replace('_', ' ').title()
        print(f"\n{category_title.upper()} ({len(locations)} locations)")
        print("-" * 40)
        
        for i, location in enumerate(locations, 1):
            name = location['name']
            location_type = location.get('type', 'Unknown')
            area = location.get('area', 'Unknown Area')
            
            print(f"{i:2d}. {name} ({location_type})")
            
            # Show population if available
            if 'population_2021' in location:
                pop = f"{location['population_2021']:,}"
                print(f"    Population (2021): {pop}")
            
            # Show area
            if area != 'Unknown Area':
                print(f"    Area: {area}")
            
            # Show sub-areas if available
            if 'sub_areas' in location and location['sub_areas']:
                sub_areas = ', '.join(location['sub_areas'])
                print(f"    Sub-areas: {sub_areas}")
            
            # Show coordinates if available
            if 'coordinates' in location:
                coords = location['coordinates']
                print(f"    Coordinates: {coords['lat']}, {coords['lng']}")
            
            print()

def display_simple_list():
    """Display just the names of all locations"""
    data = load_locations()
    
    print("=" * 60)
    print("SIMPLE LIST OF ALL LOCATIONS IN ACCRA, GHANA")
    print("=" * 60)
    
    all_locations = []
    
    # Collect all main locations
    for category_name, locations in data['categories'].items():
        for location in locations:
            all_locations.append(location['name'])
            
            # Add sub-areas
            if 'sub_areas' in location and location['sub_areas']:
                for sub_area in location['sub_areas']:
                    all_locations.append(f"  └─ {sub_area}")
    
    # Sort and display
    all_locations.sort()
    for i, location in enumerate(all_locations, 1):
        print(f"{i:3d}. {location}")
    
    print(f"\nTotal: {len(all_locations)} locations")

def display_by_area():
    """Display locations grouped by area"""
    data = load_locations()
    
    print("=" * 60)
    print("LOCATIONS BY AREA")
    print("=" * 60)
    
    areas = {}
    
    # Group by area
    for category_name, locations in data['categories'].items():
        for location in locations:
            area = location.get('area', 'Unknown Area')
            if area not in areas:
                areas[area] = []
            areas[area].append(location['name'])
    
    # Display by area
    for area in sorted(areas.keys()):
        print(f"\n{area.upper()}")
        print("-" * 30)
        for i, location in enumerate(sorted(areas[area]), 1):
            print(f"{i:2d}. {location}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "simple":
            display_simple_list()
        elif sys.argv[1] == "area":
            display_by_area()
        else:
            print("Usage: python display_locations.py [simple|area]")
            print("  simple - Show simple list of all locations")
            print("  area   - Show locations grouped by area")
            print("  (no args) - Show detailed information")
    else:
        display_all_locations()
