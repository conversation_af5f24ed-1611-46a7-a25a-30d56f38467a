import json
import time
import geonamescache
import osmnx as ox
import overpy
import requests
from typing import Dict, List, Set
import logging
from decimal import Decimal
from shapely.geometry import Point, LineString, Polygon, MultiPolygon
import pandas as pd

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DecimalEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle Decimal types and geometric objects"""

    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)

        # Handle shapely geometric objects specifically
        if isinstance(obj, Point):
            return {'lat': obj.y, 'lng': obj.x}
        elif isinstance(obj, (LineString, Polygon, MultiPolygon)):
            return str(obj)  # Convert complex geometries to string

        # Handle pandas/numpy types
        if hasattr(obj, 'item'):  # numpy scalars
            return obj.item()
        if isinstance(obj, pd.Timestamp):
            return obj.isoformat()

        # Handle other objects with coordinate attributes
        if hasattr(obj, 'x') and hasattr(obj, 'y') and hasattr(obj, 'coords'):
            return {'lat': float(obj.y), 'lng': float(obj.x)}

        # Convert other non-serializable objects to strings
        if not isinstance(obj, (str, int, float, bool, list, dict, type(None))):
            return str(obj)

        return super().default(obj)


class AccraLocationCollector:
    def __init__(self):
        self.locations = set()
        self.detailed_locations = []
        self.gc = geonamescache.GeonamesCache()

        # Accra bounding box (approximate)
        self.accra_bbox = {
            'north': 5.8,
            'south': 5.3,
            'east': 0.2,
            'west': -0.6
        }

        # Configure OSMnx settings
        ox.settings.use_cache = True
        ox.settings.log_console = True

    def clean_tags(self, tags_dict):
        """Clean tags dictionary to remove non-serializable objects"""
        cleaned_tags = {}
        for key, value in tags_dict.items():
            if isinstance(value, (str, int, float, bool, type(None))):
                cleaned_tags[key] = value
            elif isinstance(value, (list, tuple)):
                # Convert list/tuple to string if it contains non-serializable objects
                try:
                    json.dumps(value)  # Test if serializable
                    cleaned_tags[key] = value
                except (TypeError, ValueError):
                    cleaned_tags[key] = str(value)
            else:
                # Convert everything else to string
                cleaned_tags[key] = str(value)
        return cleaned_tags

    def get_geonames_data(self):
        """Get locations from GeoNames cache"""
        logger.info("Fetching data from GeoNames cache...")

        # Get all cities in Ghana
        cities = self.gc.get_cities()
        ghana_cities = []

        for city_id, city_data in cities.items():
            if city_data.get('countrycode') == 'GH':
                try:
                    lat = float(city_data.get('latitude', 0))
                    lon = float(city_data.get('longitude', 0))

                    # Check if within Accra region (rough bounds)
                    if (self.accra_bbox['south'] <= lat <= self.accra_bbox['north'] and
                            self.accra_bbox['west'] <= lon <= self.accra_bbox['east']):

                        location = {
                            'name': city_data.get('name'),
                            'type': 'City/Town',
                            'source': 'GeoNames',
                            'population': int(city_data.get('population', 0)),
                            'coordinates': {'lat': lat, 'lng': lon},
                            'admin1': city_data.get('admin1code'),
                            'timezone': city_data.get('timezone')
                        }
                        ghana_cities.append(location)
                        self.locations.add(city_data.get('name'))
                except (ValueError, TypeError) as e:
                    logger.warning(
                        f"Error processing city data for {city_data.get('name', 'unknown')}: {e}")
                    continue

        logger.info(f"Found {len(ghana_cities)} cities/towns from GeoNames")
        return ghana_cities

    def get_osm_places(self):
        """Get places from OpenStreetMap using OSMnx"""
        logger.info("Fetching places from OpenStreetMap using OSMnx...")

        osm_locations = []

        try:
            # Define the area around Accra
            place_name = "Accra, Ghana"

            # Get various types of places
            place_types = [
                'place',
                'amenity',
                'landuse',
                'natural',
                'tourism',
                'shop',
                'office'
            ]

            for place_type in place_types:
                try:
                    logger.info(f"Fetching {place_type} data...")

                    # Get geometries for the place type
                    gdf = ox.features_from_place(
                        place_name,
                        tags={place_type: True}
                    )

                    for idx, row in gdf.iterrows():
                        name = row.get('name')
                        if name and isinstance(name, str) and len(name.strip()) > 0:
                            try:
                                # Get centroid coordinates
                                if hasattr(row.geometry, 'centroid'):
                                    centroid = row.geometry.centroid
                                    lat, lon = float(
                                        centroid.y), float(centroid.x)
                                else:
                                    lat, lon = float(row.geometry.y), float(
                                        row.geometry.x)

                                # Clean the tags to ensure JSON serializability
                                clean_row_data = {}
                                for k, v in row.dropna().items():
                                    if k != 'geometry':  # Skip geometry column
                                        clean_row_data[k] = v

                                location = {
                                    'name': name.strip(),
                                    'type': f"OSM_{place_type}",
                                    'source': 'OpenStreetMap',
                                    'coordinates': {'lat': lat, 'lng': lon},
                                    'osm_id': str(idx),
                                    'tags': self.clean_tags(clean_row_data)
                                }

                                if name.strip() not in self.locations:
                                    osm_locations.append(location)
                                    self.locations.add(name.strip())

                            except (ValueError, TypeError, AttributeError) as e:
                                logger.warning(
                                    f"Error processing OSM location {name}: {e}")
                                continue

                    # Add delay to be respectful to OSM servers
                    time.sleep(1)

                except Exception as e:
                    logger.warning(f"Error fetching {place_type}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error with OSMnx: {e}")

        logger.info(f"Found {len(osm_locations)} locations from OSMnx")
        return osm_locations

    def get_overpy_data(self):
        """Get additional data using Overpy (direct OSM API)"""
        logger.info("Fetching data using Overpy...")

        api = overpy.Overpass()
        overpy_locations = []

        # Overpass query for various place types in Accra area
        bbox = f"{self.accra_bbox['south']},{self.accra_bbox['west']},{self.accra_bbox['north']},{self.accra_bbox['east']}"

        queries = [
            f'[out:json][timeout:25];(node["place"~"city|town|village|suburb|neighbourhood|hamlet"]({bbox}););out;',
            f'[out:json][timeout:25];(node["amenity"~"school|hospital|university|market|bank"]({bbox}););out;',
            f'[out:json][timeout:25];(way["place"~"city|town|village|suburb|neighbourhood"]({bbox}););out center;',
            f'[out:json][timeout:25];(node["landuse"~"residential|commercial|industrial"]({bbox}););out;',
        ]

        for query in queries:
            try:
                logger.info("Executing Overpass query...")
                result = api.query(query)

                # Process nodes
                for node in result.nodes:
                    name = node.tags.get('name')
                    if name and name.strip() not in self.locations:
                        try:
                            location = {
                                'name': name.strip(),
                                'type': f"OSM_{node.tags.get('place', node.tags.get('amenity', node.tags.get('landuse', 'unknown')))}",
                                'source': 'Overpass',
                                'coordinates': {'lat': float(node.lat), 'lng': float(node.lon)},
                                'osm_id': str(node.id),
                                'tags': self.clean_tags(dict(node.tags))
                            }
                            overpy_locations.append(location)
                            self.locations.add(name.strip())
                        except (ValueError, TypeError) as e:
                            logger.warning(
                                f"Error processing Overpass node {name}: {e}")
                            continue

                # Process ways (for areas)
                for way in result.ways:
                    name = way.tags.get('name')
                    if name and name.strip() not in self.locations:
                        try:
                            # Use center coordinates if available
                            if hasattr(way, 'center_lat') and hasattr(way, 'center_lon'):
                                lat, lon = float(way.center_lat), float(
                                    way.center_lon)
                            else:
                                # Calculate centroid from nodes
                                if way.nodes:
                                    lats = [
                                        float(node.lat) for node in way.nodes if hasattr(node, 'lat')]
                                    lons = [
                                        float(node.lon) for node in way.nodes if hasattr(node, 'lon')]
                                    if lats and lons:
                                        lat = sum(lats) / len(lats)
                                        lon = sum(lons) / len(lons)
                                    else:
                                        continue
                                else:
                                    continue

                            location = {
                                'name': name.strip(),
                                'type': f"OSM_{way.tags.get('place', way.tags.get('landuse', 'area'))}",
                                'source': 'Overpass',
                                'coordinates': {'lat': lat, 'lng': lon},
                                'osm_id': str(way.id),
                                'tags': self.clean_tags(dict(way.tags))
                            }
                            overpy_locations.append(location)
                            self.locations.add(name.strip())
                        except (ValueError, TypeError, AttributeError) as e:
                            logger.warning(
                                f"Error processing Overpass way {name}: {e}")
                            continue

                # Add delay between queries
                time.sleep(2)

            except Exception as e:
                logger.warning(f"Error with Overpass query: {e}")
                continue

        logger.info(f"Found {len(overpy_locations)} locations from Overpass")
        return overpy_locations

    def collect_all_locations(self):
        """Collect locations from all sources"""
        logger.info(
            "Starting comprehensive location collection for Accra, Ghana...")

        all_locations = []

        # Collect from all sources
        try:
            geonames_data = self.get_geonames_data()
            all_locations.extend(geonames_data)
        except Exception as e:
            logger.error(f"Error collecting GeoNames data: {e}")

        try:
            osm_data = self.get_osm_places()
            all_locations.extend(osm_data)
        except Exception as e:
            logger.error(f"Error collecting OSM data: {e}")

        try:
            overpy_data = self.get_overpy_data()
            all_locations.extend(overpy_data)
        except Exception as e:
            logger.error(f"Error collecting Overpass data: {e}")

        # Create comprehensive dataset
        comprehensive_data = {
            'country': 'Ghana',
            'region': 'Greater Accra Region',
            'capital': 'Accra',
            'description': 'Comprehensive list of locations in Accra, Ghana from multiple data sources',
            'last_updated': time.strftime('%Y-%m-%d'),
            'total_locations': len(all_locations),
            'data_sources': ['GeoNames', 'OpenStreetMap', 'Overpass'],
            'bounding_box': self.accra_bbox,
            'locations': all_locations
        }

        return comprehensive_data

    def save_data(self, data, filename='comprehensive_accra_locations.json'):
        """Save the collected data to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False,
                          cls=DecimalEncoder)

            logger.info(f"Data saved to {filename}")
            logger.info(
                f"Total locations collected: {data['total_locations']}")

            # Print summary by source
            sources = {}
            for location in data['locations']:
                source = location.get('source', 'Unknown')
                sources[source] = sources.get(source, 0) + 1

            print("\nSummary by data source:")
            for source, count in sources.items():
                print(f"  {source}: {count} locations")

        except Exception as e:
            logger.error(f"Error saving data: {e}")
            # Try to save a minimal version without problematic data
            try:
                minimal_data = {
                    'total_locations': data['total_locations'],
                    'error': 'Full data could not be serialized',
                    'location_names': [loc.get('name', 'Unknown') for loc in data['locations']]
                }
                with open(f"minimal_{filename}", 'w', encoding='utf-8') as f:
                    json.dump(minimal_data, f, indent=2, ensure_ascii=False)
                logger.info(f"Minimal data saved to minimal_{filename}")
            except Exception as e2:
                logger.error(f"Could not save even minimal data: {e2}")


def main():
    collector = AccraLocationCollector()

    try:
        data = collector.collect_all_locations()
        collector.save_data(data)

        # Create a simple names list
        location_names = sorted(list(collector.locations))

        print(f"\nFirst 50 location names:")
        for i, name in enumerate(location_names[:50], 1):
            print(f"{i:2d}. {name}")

        if len(location_names) > 50:
            print(f"... and {len(location_names) - 50} more locations")

    except Exception as e:
        logger.error(f"Error in main execution: {e}")


if __name__ == "__main__":
    main()
